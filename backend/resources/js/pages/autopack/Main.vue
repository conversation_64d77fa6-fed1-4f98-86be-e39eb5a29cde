<template>
  <div class="card">

    <div class="card-block b-t-default p-t-10">
      <div class="row top--row--additions m-b-10">
          <div class="col-md-12 p-l-0 p-r-0" v-if="showAdditions">
            <div class="col-md-2 p-l-0 p-r-0">
              <div class="col-md-12 p-l-0 p-r-0" v-for="(stage) in stageList" :key="`stageList-${stage.id}`">
                <app-switch classes="is-warning" :type="'checkbox'" :name="'stage'" :checked="parseInt(selectedStage)===parseInt(stage.id)" :dataVal="stage.id" @click="selectStage(stage)">{{stage.title}}</app-switch>
              </div>
            </div>
            <div class="col-md-8 p-l-0 p-r-0">
              <div class="col-md-3 p-l-0 p-r-0" v-for="(addition) in additionList" :key="`addition-${addition.id}`">
                <app-switch classes="is-warning" :type="'checkbox'" :name="'additions[]'" :checked="selectedAdditions.indexOf(addition.id) != -1" :value="addition.id" @click="selectAddition(addition)">{{addition.title}}</app-switch>
              </div>
            </div>
            <div class="col-md-2 p-l-0 p-r-0" v-if="showModBtn">
              <file-input :type="'modified'" :stage_id="selectedStage" :additions="selectedAdditions" :params="selectedParams" @uploaded="uploadedMod($event)"></file-input>
            </div>
          </div>
        <div class=" col-md-12 p-l-0 p-r-10">
          <div class="col-md-3 p-l-10 p-r-10 m-b-15" v-if="showOrigBtn">
            <label class="control-label">S/n</label>
            <input  class="form-control" type="text" v-model="selectedParams.soft_num"/>
          </div>
          <div class="col-md-3 p-l-10 p-r-10 m-b-15" v-if="showOrigBtn">
            <label class="control-label">H/n</label>
            <input  class="form-control" type="text" v-model="selectedParams.hard_num"/>
          </div>
          <div class="col-md-3 p-l-10 p-r-10 m-b-15" v-if="showOrigBtn">
            <label class="control-label">Chcksum</label>
            <input  class="form-control" type="text" v-model="selectedParams.check_sum"/>
          </div>
          <!--        <div class="col-md-12 p-l-0 p-r-0 m-b-15" v-if="showEditBtn">-->
          <!--          <label class="control-label">Title</label>-->
          <!--          <input  class="form-control"  type="text" v-model="selectedParams.title" v-on:keyup="saveSelectedParams"/>-->
          <!--        </div>-->
          <div class="col-md-3 p-l-0 p-r-0">
            <div class="col-md-12" v-if="showOrigBtn">
              <file-input :type="'original'" :params="selectedParams" :ecu_id="selectedEcuItem" :group_id="selectedSubGroupItem" @uploaded="uploadedOrig($event)" @change="fileSelected($event)"></file-input>
            </div>
          </div>
          <div class="col-md-12 text-right">
            <button type="button" v-if="showDelBtn"  class="btn btn-danger m-b-20" @click="deleteFile()">Delete File <i class="fa fa-trash"></i></button>
            <button type="button" v-if="showDownloadBtn"  class="btn btn-default m-b-20" @click="downloadFile()">Download File <i class="fa fa-download"></i></button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-2 p-l-0 p-r-10 m-b-15">
          <label class="control-label">ECU:</label>
          <v-select-own :options="ecuList" label="title" :reduce="title => title.id" v-model="selectedEcuItem"></v-select-own>
        </div>
        <div class="col-md-3 p-l-0 p-r-10 m-b-15">
          <label class="control-label">Group:</label>
          <v-select-own :options="groupList" label="title" :reduce="title => title.id" v-model="selectedGroupItem"></v-select-own>
        </div>
        <div class="col-md-2 p-l-0 p-r-10 m-b-15">
          <label class="control-label">Subgroup:</label>
          <v-select-own :options="subGroupList" label="title" :reduce="title => title.id" v-model="selectedSubGroupItem"></v-select-own>
        </div>
        <div class="col-md-2 p-l-0 p-r-10 m-b-15">
          <label class="control-label">SoftWare Number:</label>
          <div class="m-t-5 text-primary f-14"><input type="text" v-model="filter.soft_num"/></div>
        </div>
        <div class="col-md-2 p-l-0 p-r-10 m-b-15">
          <label class="control-label">HardWare Number:</label>
          <div class="m-t-5 text-primary f-14"><input type="text" v-model="filter.hard_num"/></div>
        </div>
        <div class="col-md-1 p-l-0 p-r-10 m-b-15">
          <label class="control-label">Checksum:</label>
          <div class="m-t-5 text-primary f-14"><input style="width: 80px" type="text" v-model="filter.check_sum"/></div>
        </div>
      </div>
      <div class="col-md-12 p-l-0 p-r-0">
        <v-jstree :key="componentKey" :data="filteredFiles" allow-transition allow-batch @item-click="itemClick">
          <template scope="_">
            <div class="col-md-7 f--15">
              <i :class="_.vm.themeIconClasses" role="presentation" v-if="!_.model.loading"></i>
              {{_.model.text}}
            </div>
            <div class="col-md-2">
              {{_.model.soft_num}}
            </div>
            <div class="col-md-2">
              {{_.model.hard_num}}
            </div>
            <div class="col-md-1">
              {{_.model.check_sum}}
            </div>
          </template>
        </v-jstree>
      </div>
    </div>
  </div>
</template>

<script>
  import VJstree from 'vue-jstree'
  import FileInput from './components/FileInput.vue'
  import Switch from './components/Switch'
  export default {
  components: {
    VJstree,
    'app-switch': Switch,
    FileInput
  },
  props: [],
  name: "Main",
  data: function () {
    return {
      ecuList: [],
      groupList: [],
      subGroupList: [],

      // selectedEcu: 0,

      selectedEcuItem: null,
      selectedGroupItem: null,
      selectedSubGroupItem: null,
      filesTree: [],
      selectedTreeItem: null,

      // selectedGroup: null,
      // groupsTree: [],
      selectedAdditions: [],
      selectedStage: 0,
      selectedParams: {
        'soft_num': '',
        'hard_num': '',
        'check_sum': '',
        'group_id': 0,
        'ecu_id': 0,
      },
      filter: {
        'soft_num': '',
        'hard_num': '',
        'check_sum': '',
      },
      // showEditBtn: false,
      // showModBtn: false,
      stageList: [],
      additionList: [],
      componentKey: 0,
    }
  },
  computed: {
    showOrigBtn: function(){
      return this.selectedEcuItem !== null && this.selectedSubGroupItem !== null && !this.showEditBtn
    },
    showModBtn: function(){
      return this.selectedParams !== null && !this.selectedParams.isLeaf;//(this.selectedGroup !== null && this.selectedGroup.type == 'file' && !this.selectedGroup.isLeaf) ? true : false
    },
    showEditBtn: function(){
      return this.selectedParams !== null && this.selectedParams.id > 0;//(this.selectedGroup !== null && this.selectedGroup.type == 'file' && !this.selectedGroup.isLeaf) ? true : false
    },
    showDelBtn: function(){
      return this.selectedParams !== null && this.selectedParams.id > 0//(this.selectedGroup !== null && this.selectedGroup.type == 'file') ? true : false
    },
    showAdditions: function(){
      return !this.showOrigBtn && this.selectedParams !== null && this.selectedParams.id > 0//(this.selectedGroup !== null && this.selectedGroup.type == 'file') ? true : false
    },
    showDownloadBtn: function(){
      return this.showDelBtn
    },
    filteredFiles: function () {
      return this.filesTree.filter((fileItem) => {
        // if (!fileItem.isLeaf) {
        //   // if (this.selectedTreeItem !== null && fileItem.id == this.selectedTreeItem.id) {
        //   //   fileItem.opened = true
        //   //   // fileItem.selected = true
        //   // }
        // }
        return Object.entries(this.filter).every(([key, value]) => {
          return fileItem[key].toLowerCase().indexOf(value.toLowerCase()) > -1;
        });
      })
    }
  },
    watch: {
      selectedEcuItem: {
        handler: function (val) {
          this.getFilesTree()
        }
      },
      selectedGroupItem: {
        handler: function (val) {
          this.getSubGroupList()
        }
      },
      selectedSubGroupItem: {
        handler: function (val) {
          this.getFilesTree()
        }
      },
    },
    methods: {
      async getFilesTree(){

        this.resetSelectedParams()
        this.filesTree = []
        if (this.selectedEcuItem !== null && this.selectedSubGroupItem !== null) {
          await axios.get('files-tree?ecu_id=' + this.selectedEcuItem + '&subgroup_id=' + this.selectedSubGroupItem)
                  .then((response) => {
                    this.filesTree = response.data.content
                  }).catch(function (e) {
                    console.log(e.message)
                  })
        }
      },

      async getSubGroupList(){
        if (this.selectedGroupItem !== null) {
          await axios.get('sub-group-list?group_id=' + this.selectedGroupItem)
                  .then((response) => {
                    this.subGroupList = response.data.content
                  }).catch(function (e) {
                    console.log(e.message)
                  })
          this.selectedSubGroupItem = this.subGroupList.length > 0 ? this.subGroupList[0].id : null
        }
      },

      itemClick (node, item, e) {

        if (node.model.id == this.selectedParams.id) {
          this.resetSelectedParams()
          node.model.selected = false
        } else {
          this.resetSelectedParams()
          this.componentKey += 1;
          this.selectedParams = node.model
          this.selectedTreeItem = node.model
          node.model.opened = true
          if (node.model.isLeaf){
            this.selectedStage = node.model.stage_id
            this.selectedAdditions = node.model.additions.split(',')
          } else {
            this.resetSelectedModParams()
          }
        }
      },

      async deleteFile(){
         if (this.selectedParams && confirm('Are you sure?')) {
          await axios.get('delete-file?id='+this.selectedParams.id)
                  .then((response) => {
                    if(response.data.status == 'success') {
                      // this.resetSelectedParams()
                      this.getFilesTree()
                    }
                  }).catch(function (e) {
                    console.log(e.message)
                  })
         }
      },

      async downloadFile(){
        if (this.selectedParams) {

          await axios.get('download-file?id='+this.selectedParams.id,{
            responseType: 'arraybuffer',
            headers: {
              'Content-Type': 'application/json',
            }
          })
                  .then((response) => {
                    var fileURL = window.URL.createObjectURL(new Blob([response.data]));
                    var fileLink = document.createElement('a');

                    fileLink.href = fileURL;
                    fileLink.setAttribute('download', this.selectedParams.title);
                    document.body.appendChild(fileLink);

                    fileLink.click();
                  }).catch(function (e) {
                    console.log(e.message)
                  })
        }
      },

      uploadedOrig(uploadResponse) {
        if (uploadResponse.data.status == 'success') {
          // this.resetSelectedParams()
          this.getFilesTree()

        } else {
          console.log(uploadResponse.data.content);
        }

      },

      fileSelected(event) {
        console.log(event);
      },

      uploadedMod(uploadResponse) {
        if (uploadResponse.data.status == 'success') {
          this.selectedParams.children.push(uploadResponse.data.content)
          // this.resetSelectedParams()
          this.resetSelectedModParams()
          // this.getFilesTree()
        } else {
          console.log(uploadResponse.data.content);
        }
      },

      selectAddition(item)
      {
        this.selectedAdditions.indexOf(item.id) != -1 ? this.selectedAdditions.splice(this.selectedAdditions.indexOf(item.id), 1) : this.selectedAdditions.push(item.id)
      },

      selectStage(item)
      {
        this.selectedStage = item.id
      },

    selectEcu(item)
    {
      this.selectedParams.ecu_id = item.value
    },

      async getEcuList(){
        await axios.get('ecu-list')
                  .then((response) => {
                    this.ecuList = response.data.content
                  }).catch(function (e) {
                  console.log(e.message)
                })
      },

      async getGroupList(){
        await axios.get('group-list')
                .then((response) => {
                  this.groupList = response.data.content
                }).catch(function (e) {
                  console.log(e.message)
                })
      },


      async getEcuFiles(){
        if (this.selectedEcu) {
          await axios.get('ecu-files?ecu_id='+this.selectedEcu)
                    .then((response) => {
                      this.ecuList = response.data.content
                      resolve(response.data.content)
                    }).catch(function (e) {
                    console.log(e.message)
                  })
        }
      },

      async getGroups(){
        await axios.get('group-tree')
                .then((response) => {
                  this.groupsTree = response.data.content
                }).catch(function (e) {
                  console.log(e.message)
                })
      },

      async getStages(){
        await axios.get('stage-list')
                .then((response) => {
                  this.stageList = response.data.content
                }).catch(function (e) {
                  console.log(e.message)
                })
      },

      async getAdditions(){
        await axios.get('addition-list')
                .then((response) => {
                  this.additionList = response.data.content
                }).catch(function (e) {
                  console.log(e.message)
                })
      },

      resetSelectedParams() {
        this.selectedParams = {
          'soft_num': '',
          'hard_num': '',
          'check_sum': '',
          'group_id': 0,
          'ecu_id': 0,
        }

      },

      resetSelectedModParams() {
        this.selectedStage = -1
        this.selectedAdditions = []
      }

    },

  mounted() {
    this.getEcuList()
    this.getGroupList()
    this.getEcuFiles()
    this.getStages()
    this.getAdditions()
  }
}
</script>

<style>
  .f--15{
    font-size: 15px;
  }
  .tree-anchor{
    width: 100%;
  }
  .top--row--additions{
    min-height: 170px;
    border-bottom: 1px solid;

  }
  .file--params{
    color:#ffa700;
  }
  .file--ecu{
    color: #0fb4ff;
  }
  .file--name{
    color: #ab03ff;
  }
  .vs--single .vs__selected{
    background-color: #2a436a!important;
  }
  .param__text{
    justify-content: center;
    display: flex;
    flex-direction: column;
  }
.pcoded .pcoded-navbar {
  z-index: 600 !important;
}
  .tree-selected{
    color:#8bc4ea !important;
  }
.standardTreeClass{
  background-color:transparent !important;
}
.changedTreeClass{
  background-color:transparent !important;
}
  .tree-hovered{
    background-color:#85a5f0  !important;
    color:#110a0a!important;
  }
.tree-selected{
  background-color: #5e3333 !important;
  color:#f1f1f1!important;
}
  .left_column div {
    min-height: 45px;
  }
.vs__selected{
  color:#ffffee!important;
}
.vs__actions svg{
  fill:#ffffee!important;
}
</style>
