<?php

use common\models\ChipBrand;
use common\models\ChipEcuDict;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use common\models\ChipReadmethod;
use common\models\ChipVehicle;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;
use yii\web\View;

/* @var $this yii\web\View */
/* @var $model common\models\Projects */
/* @var $form yii\bootstrap\ActiveForm */
if (Yii::$app->user->can('msdealer')) {

$scriptMsDealer = <<< JS
     // метод выбора всех чекбоксов со сменой состояния
    $(document).on('change', '#projects-client_name', function (e) {
        if (undefined !== $(this).val() && $(this).val().length > 5) {
            $.ajax({
                method: "GET",
                url: "find-client1c",
                dataType: "json",
                data: {name:$(this).val()},
                // processData : false,
                // dataType: 'json',
            })
                .done(function( msg ) {
                    if (msg.data.length)
                    // $('#stages_div').html(msg.priceHtml);
                    // $('#ecu_div').html(msg.ecuHtml);
                    // initSwithers();
                    console.log(msg);
                    // alert( "Data Saved: " + msg );
                    // initSwithers();
                });
            }
        // console.log($(this).val());
        return false;
    });
        // установка бэкграунда
    $('#pcoded').attr('sidebar-img-type','auto4');

JS;

$this->registerJs($scriptMsDealer, yii\web\View::POS_READY);
}

$script = <<< JS
    $('#projectForm').on('beforeSubmit', function (event) {
         console.log(event);
        // if (errorAttributes.length == 0) {
            if(document.getElementById("file_added")) {
                console.log('file_added');
                return true;
            } else {
                 console.log('file_not_added');
                 if (confirm("Are you sure create project without file?")) {
                     return true;
                 }
               // swal({
               //      title: "Are you sure create project without file?",
               //      // text: "Your will be able to recover this option later!",
               //      type: "warning",
               //      showCancelButton: true,
               //      confirmButtonClass: "btn btn-danger",
               //      confirmButtonText: "Yes, create project!",
               //      closeOnConfirm: true,
               //  },
               //  function(isConfirm) {
               //          console.log('isConfirm');
               //          console.log(isConfirm);
               //      if (isConfirm) {
               //          console.log('return true');
               //          return true;
               //      }
               //  });
                return false;
            }
            // return true;
        // }
        return false;
    });
    $('#projectForm').on('beforeValidate', function (e) {
                // console.log(' projectForm beforeValidate');
        // var ecu = '';
          if(document.getElementById("ecu_id")) {
              if ($('#ecu_id').val() > 0) {
                 return true;
             } else {
                 if(document.getElementById("projects-custom_ecu")) {
                     if ($('#projects-custom_ecu').val().length > 0) {
                         return true;
                     } else {
                         swal("Error", "Select or type Ecu!", "error");
                         return false;
                     }
                 }
             }
         }
         return true;
    });

    $('#engine_hp').on('keyup', function (e) {
        console.log(this.value);
        $('#engine_kwt').val(parseInt(this.value/1.341));
    });
    
    $('#engine_kwt').on('keyup', function (e) {
        console.log(this.value);
        $('#engine_hp').val(parseInt(this.value*1.341));
    });
    
    function getProjectAllData(params) {
        $('#stages_div').html('');
        $('#ecu_div').html('');
       console.log(params);
        // var json = JSON.parse(params);
        // console.log(json);
        $.ajax({
            method: "POST",
            url: "get-all-data/",
            data: params,
            // processData : false,
            // dataType: 'json',
        })
            .done(function( msg ) {
                $('#stages_div').html(msg.priceHtml);
                $('#ecu_div').html(msg.ecuHtml);
                // initSwitchers();
                // console.log();
                // alert( "Data Saved: " + msg );
                initSwitchers();
            });
    }
    function clearYear() {
        // console.log('yesr select');
        $("#year").val("").trigger('change');
    }
    function getStages() {
        // console.log('getStages');
        var params = {};
        params.vehicle_id = $("#vehicle_id").val();
        params.brand_id = $("#brand_id").val();
        
        params.model_id = $("#model_id").val();
        // console.log(model_id);
        
        params.generation_id = $("#generation_id").val();
        // console.log(generation_id);
        
        params.engine_id = $("#engine_id").val();
        // console.log(engine_id);
        
        params.ecu_id = $("#ecu_id").val();
        // console.log(ecu_id);
        console.log(params);
        
         // getStagesList(params);
         getProjectAllData(params);
    }
JS;
//маркер конца строки, обязательно сразу, без пробелов и табуляции
$this->registerJs($script, yii\web\View::POS_READY);
//print_r(Yii::$app->params['years']);
//die;<style>.kv-file-remove{display:none;}</style>change_additions_modal
?>

<div class="projects-form">

    <?php $form = ActiveForm::begin([
        'id'                   => 'projectForm',
        'options'              => ['accept-charset'=>'utf-8', 'method' => 'post'],
        'enableAjaxValidation' => true,
        'enableClientValidation' => false,
//        'validateOnSubmit' => false,
    ]); ?>

    <?=$form->field($model, 'client_1c')->hiddenInput(['id' => 'client_1c'])->label(false)?>

    <?= Html::errorSummary($model, ['encode' => false]) ?>

    <div class="row">
        <div class="col-md-12 col-lg-12 col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h5><?=Yii::t('backend', 'Vehicle and Client information')?></h5>
                </div>
                <div class="card-block b-t-default p-t-10">
                    <div class="row">
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'vehicle_id')->widget(Select2::classname(), [
                                'theme' => Select2::THEME_KRAJEE,
                                'language' => 'ru',
                                'data' => ArrayHelper::map(common\models\ChipVehicle::find()->notDeleted()->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'id'=>'vehicle_id'
                                ],
                                'pluginEvents' => [
                                    "change" => "function() { console.log(this.value); clearYear(); }",
                                ],
                                'pluginOptions' => [
                                    'allowClear' => true,
                                    'initialize' => true,
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'brand_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
//                                'data' => ArrayHelper::map(common\models\ChipBrand::find()->notDeleted()->orderBy('title')->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                //                        'multiple' => true,
                                    'id'=>'brand_id'
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginEvents' => [
                                    "change" => "function() { console.log(this.value); clearYear(); }",
                                ],
                                'pluginOptions' => [
                                    'depends' => ['vehicle_id'],
                                    'placeholder' => Yii::t('backend', 'Select brand'),
                                    'url' => Url::to(['/chip-brand/json-list-brand']),
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'model_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
//                                'data' => ArrayHelper::map(common\models\ChipModel::find()->notDeleted()->orderBy('title')->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'id'=>'model_id'
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginEvents' => [
                                    "change" => "function() { console.log(this.value); clearYear(); }",
                                ],
                                'pluginOptions' => [
                                    'depends' => ['brand_id'],
                                    'placeholder' => Yii::t('backend', 'Select Brand first'),
                                    'url' => Url::to(['/chip-model/json-list']),
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'generation_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
//                                'data' => ArrayHelper::map(common\models\ChipGeneration::find()->notDeleted()->orderBy('title')->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'id'=>'generation_id'
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginEvents' => [
                                    "change" => "function() { console.log(this.value); clearYear(); }",
                                ],
                                'pluginOptions' => [
                                    'depends'=>['model_id'],
                                    'placeholder' => Yii::t('backend', 'Select Model first'),
                                    'url'=>Url::to(['/chip-generation/json-list']),
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'engine_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
//                                'data' => ArrayHelper::map(common\models\ChipEngine::find()->notDeleted()->orderBy('title')->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'id'=>'engine_id'
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginEvents' => [
                //                    "change" => "function() { console.log(this.value); getStages(); }",
                                ],
                                'pluginOptions' => [
                                    'depends'=>['generation_id'],
                                    'placeholder' => Yii::t('backend', 'Select Generation first'),
                                    'url'=>Url::to(['/chip-engine/json-list']),
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'ecu_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
//                                'data' => ArrayHelper::map(common\models\ChipEcuDict::find()->notDeleted()->orderBy('title')->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'id'=>'ecu_id'
                                ],
                                'pluginEvents' => [
                                    "change" => "function() { console.log(this.value); getStages(); }",
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginOptions' => [
                                    'depends'=>['engine_id'],
                                    'placeholder' => Yii::t('backend', 'Select Engine first'),
                                    'url'=>Url::to(['/chip-ecu/json-list']),
                                ],
                            ]); ?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-lg-3 col-sm-4">
                            <?php echo $form->field($model, 'vin_num')->textInput(['maxlength' => true]) ?>
                        </div>
                        <div class="col-md-3 col-lg-3 col-sm-4">
                            <?php echo $form->field($model, 'registration_num')->textInput() ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'year')->widget(Select2::classname(), [
                                'language' => 'ru',
                                'data' => Yii::$app->params['years'],
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'id' => 'year',
                                ],
                                'pluginEvents' => [
                                ],
                                'pluginOptions' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'gearbox_id')->widget(Select2::classname(), [
                                'language' => 'ru',
                                'data' => ArrayHelper::map(\common\models\ChipGearbox::find()->notDeleted()->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                ],
                                'pluginEvents' => [
                                    //                            "change" => "function() { console.log(this.value); getStages(); }",
                                ],
                                //                        'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginOptions' => [
                                    //                            'depends'=>['engine_id'],
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    //                            'url'=>Url::to(['/chip-ecu/json-list']),
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?php echo $form->field($model, 'custom_ecu')->textInput(['id' => 'custom_ecu']) ?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-lg-3 col-sm-4">
                            <?php echo $form->field($model, 'order_1c')->textInput(['id' => 'order_1c']) ?>
                        </div>
                        <div class="col-md-3 col-lg-3 col-sm-4">
                            <?php echo $form->field($model, 'client_name')->textInput(['id' => 'client_name']) ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-2">
                            <?php echo $form->field($model, 'engine_hp')->textInput(['maxlength' => true, 'id' => 'engine_hp']) ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-2">
                            <?php echo $form->field($model, 'engine_kwt')->textInput(['maxlength' => true, 'id' => 'engine_kwt'])->label(Yii::t('backend', 'Engine KW')) ?>
                        </div>
                    </div>
                    <div class="row">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 col-lg-12 col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h5><?=Yii::t('backend', 'TUNING TYPE and OPTIONS')?></h5>
                </div>
                <div class="card-block b-t-default p-t-10">
                    <div class="row">
                        <div class="col-md-12 col-lg-12 col-xl-6 col-sm-12" id="stages_div"></div>
                        <div class="col-md-12 col-lg-12 col-xl-6 col-sm-12" id="ecu_div"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12 col-lg-12 col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h5><?=Yii::t('backend', 'Tuning Tool')?></h5>
                </div>
                <div class="card-block b-t-default p-t-10">
                    <div class="row">
                        <div class="col-md-6 col-lg-6 col-sm-4">
                            <?= $form->field($model, 'master')->widget(Select2::classname(), [
                                'theme' => Select2::THEME_KRAJEE,
                                'language' => 'ru',
                                'data' => [1 => 'Master', 0 => 'Slave'],
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'id' => 'master_slave',
                                ],
                                'pluginEvents' => [
                                    //                    "change" => "function() { console.log(this.value); getStages(); }",
                                ],
                                'pluginOptions' => [
                                    'allowClear' => true,
                                    'initialize' => true,
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-6 col-lg-6 col-sm-4">
                            <?= $form->field($model, 'readmethod_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
                                'data' => ArrayHelper::map(common\models\ChipReadmethod::find()->orderBy('title')->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                ],
                                'pluginEvents' => [
//                                    "change" => "function() { console.log(this.value); getStages(); }",
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginOptions' => [
                                    'depends'=>['master_slave'],
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'url'=>Url::to(['/chip-readmethod/json-list']),
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-6 col-lg-6 col-sm-4">
                            <?php echo $form->field($model, 'hard_num')->textInput() ?>
                        </div>
                        <div class="col-md-6 col-lg-6 col-sm-4">
                            <?php echo $form->field($model, 'soft_num')->textInput() ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12 col-lg-12 col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h5><?=Yii::t('backend', 'Select File')?></h5>
                </div>
                <div class="card-block b-t-default p-t-10">
                    <div class="row">
                        <div class="col-md-12 col-lg-12 col-sm-12">
                            <div class="form-group">
                                <?= \kartik\widgets\FileInput::widget([
                                    'name' => 'file',
                                    'options' => ['multiple' => true],
                                    'bsVersion' => '3.x',
                                    'pluginOptions' => [
                                        'maxFileCount' => 4,
                                        'validateInitialCount' => true,
                                        'required' => true,
                                        'overwriteInitial' => false,
                                        'initialPreviewAsData'=>false,
//                                        'initialPreview'=> !empty($model->fileData['materials']) ? $model->fileData['materials']['filepath'] : '',
//                                        'initialPreviewConfig' => !empty($model->fileData['materials']) ? $model->fileData['materials']['preview_config'] : '',
                                        'uploadExtraData' => [
//                                            'model' => $model::className(),
                                            'model_id' => $model->id,
                                            'type' => 'init',
                                        ],
//                                        'overwriteInitial' => false,
//                                        'initialPreviewAsData'=>true,
//                                        'initialPreviewFileType' => 'image',
//                                        'initialPreviewDownloadUrl' => Url::to(['/web/uploads/{filename}']),
                                        'uploadUrl' => Url::to(['/site/file-upload']),
//                                        'deleteUrl' => Url::to(['/ajax/file-delete']),
//                                        'previewFileType' => 'any',
//                                        'maxFileCount' => 10,
                                        'showCaption' => false,
                                        'showRemove' => false,
                                        'showUpload' => false,
                                        'showDownload' => false,
//                                        'showPreview' => false,
                                        'showCancel' => false,
                                        'showDelete' => false,
                                        'browseClass' => 'btn btn-link ms-color btn-block',
                                        'browseIcon' => '<i class="fa fa-upload"></i> ',
                                        'browseLabel' =>  Yii::t('backend', 'Select file'),
                                        'previewFileIcon' => '<i class="fa fa-file"></i>',
                                        'preferIconicPreview' => true,
                                        'fileActionSettings'=>[
                                                'showZoom' => false,
//                                            'downloadClass' => 'btn btn-default',
//                    'showRemove' => false,
//                                            'downloadTitle' => 'Скачать файл',
//                                            'downloadIcon' => '<i class="glyphicon glyphicon-download"></i>Скачать  ',
//                                            'zoomClass' => 'btn btn-default',
//                                            'removeClass' => 'btn btn-default',
//                                            'zoomIcon' => '<i class="glyphicon glyphicon-zoom-in"></i>',
                                        ],
                                        'previewFileIconSettings' => [
//                                            'doc' => '<i class="fa fa-file-word-o text-primary"></i>',
//                                            'docx' => '<i class="fa fa-file-word-o text-primary"></i>',
//                                            'xls' => '<i class="fa fa-file-excel-o text-success"></i>',
//                                            'xlsx' => '<i class="fa fa-file-excel-o text-success"></i>',
//                                            'ppt' => '<i class="fa fa-file-powerpoint-o text-danger"></i>',
//                                            'pptx' => '<i class="fa fa-file-powerpoint-o text-danger"></i>',
//                                            'pdf'=>'<i class="fa fa-file-pdf-o text-danger"></i>',
//                                            'zip'=>'<i class="fa fa-file-zip-o text-muted"></i>',
//                                            'html'=>'<i class="fa fa-file-code-o text-info"></i>',
//                                            'txt'=>'<i class="fa fa-file-text-o text-info"></i>',
//                                            'bin'=>'<i class="fa fa-file-text-o text-info"></i>',
//                                            'dec'=>'<i class="fa fa-file-text-o text-info"></i>',
                                        ],
                                        'allowedPreviewTypes' => [
                                             'image',
                                        ]
                                    ],
                                    'pluginEvents' => [
                                        'filebatchselected' => 'function(){$(this).fileinput("upload");}',
                                        'fileuploaded' => 'function(event, data, previewId, index){setFile(data.response.initialPreviewConfig);console.log(data.response.initialPreviewConfig);}'
                                    ],
                                ]);
                                ?>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12 col-lg-12 col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h5><?=Yii::t('backend', 'INFORMATION')?></h5>
                </div>
                <div class="card-block b-t-default p-t-10">
                    <div class="row">
                        <div class="col-md-12 col-lg-12 col-sm-12">
                            <div class="row">
                                <div class="col-md-6 col-lg-6 col-sm-12">
                                    <?= $form->field($model, 'timeframe')->widget(Select2::classname(), [
                                        'theme' => Select2::THEME_KRAJEE,
                                        'language' => 'ru',
                                        'data' => Yii::$app->params['timeframes'],
                                        'options' => [
                                            'placeholder' => Yii::t('backend', 'Select'),
                                        ],
                                        'pluginOptions' => [
                                            'allowClear' => true,
                                            'initialize' => true,
                                        ],
                                    ]); ?>
                                </div>
                                <div class="col-md-6 col-lg-6 col-sm-12">
                                    <?= $form->field($model, 'on_dyno')->widget(Select2::classname(), [
                                        'theme' => Select2::THEME_KRAJEE,
                                        'language' => 'ru',
                                        'data' => [
                                            0 => 'No',
                                            1 => 'Yes',
                                        ],
                                        'options' => [
                                            'placeholder' => Yii::t('backend', 'Select'),
                                        ],
                                        'pluginOptions' => [
                                            'allowClear' => true,
                                            'initialize' => true,
                                        ],
                                    ]); ?>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 col-lg-12 col-sm-12">
                                    <?php echo $form->field($model, 'comments')->textarea() ?>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="form-group m-b-30">
        <?php echo Html::submitButton(Yii::t('backend', 'Create'), ['class' => 'btn btn-success', 'id' => 'create_project']) ?>
    </div>
    <input type="hidden" id="stage_id" name="stage_id" value="0"/>
    <?= $this->render('/partial/project_create_additions_modal', ['model' => $model, 'additionsDataArray' => []]);?>
    <?php ActiveForm::end(); ?>

</div>
