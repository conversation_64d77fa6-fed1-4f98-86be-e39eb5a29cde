<?php

use common\models\ChipAddition;
use common\models\ChipBrand;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\ChipEcu */
/* @var $form yii\bootstrap\ActiveForm */
/* @var $viewType string */

//echo '<pre>';
//print_r($model->additionsList);
//die;
?>
<div class="row chip-ecu-admin">
    <?php $form = ActiveForm::begin([
            'enableClientValidation' => false,
    ]); ?>
    <div class="col-lg-12 col-xl-12">
    <div class="form-group">
        <?php echo Html::submitButton($model->isNewRecord ? Yii::t('backend', 'Create') : Yii::t('backend', 'Save'), ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>
    </div>
    <div class="col-lg-12 col-xl-12">
                <div class="card">
                    <div class="card-block">
                    <?php echo $form->errorSummary($model); ?>
                        <div class="row">
                            <div class="col-sm-12 col-xl-2 col-md-2">
                                <?= $form->field($model, 'brand_id')->widget(Select2::classname(), [
                                    'theme' => Select2::THEME_KRAJEE,
                                    'language' => 'ru',
                                'data' => ArrayHelper::map(common\models\ChipBrand::find()->orderBy('title')->all(),'id', 'title'),
                                    'options' => [
                                        'placeholder' => 'Выберите',
                                        'id'=>'brand_id'
                                    ],
                                    'pluginOptions' => [
                                        'allowClear' => true,
                                        'initialize' => true,
                                    ],
                                ]); ?>
                            </div>
                            <div class="col-sm-12 col-xl-2 col-md-2">
                                <?= $form->field($model, 'model_id')->widget(DepDrop::classname(), [
                                    'type' => DepDrop::TYPE_SELECT2,
                                    'language' => 'ru',
                                'data' => ArrayHelper::map(common\models\ChipModel::find()->orderBy('title')->all(),'id', 'title'),
                                    'options' => [
                                        'placeholder' => 'Выберите',
                                        'id'=>'model_id'
                                    ],
                                    'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                    'pluginOptions' => [
                                        'depends'=>['brand_id'],
                                        'placeholder'=>'Выберите марку',
                                        'url'=>Url::to(['/chip-model/json-list']),
                                    ],
                                ]); ?>
                            </div>
                            <div class="col-sm-12 col-xl-2 col-md-2">
                                <?= $form->field($model, 'generation_id')->widget(DepDrop::classname(), [
                                    'type' => DepDrop::TYPE_SELECT2,
                                    'language' => 'ru',
                                'data' => ArrayHelper::map(common\models\ChipGeneration::find()->orderBy('title')->all(),'id', 'title'),
                                    'options' => [
                                        'placeholder' => 'Выберите',
                                        'id'=>'generation_id'
                                    ],
                                    'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                    'pluginOptions' => [
                                        'depends'=>['model_id'],
                                        'placeholder'=>'Выберите модель',
                                        'url'=>Url::to(['/chip-generation/json-list']),
                                    ],
                                ]); ?>
                            </div>
                            <div class="col-sm-12 col-xl-2 col-md-2">
                                <?= $form->field($model, 'engine_id')->widget(DepDrop::classname(), [
                                    'type' => DepDrop::TYPE_SELECT2,
                                    'language' => 'ru',
                                'data' => ArrayHelper::map(common\models\ChipEngine::find()->orderBy('title')->all(),'id', 'title'),
                                    'options' => [
                                        'placeholder' => 'Выберите',
                                        'id'=>'engine_id'
                                    ],
                                    'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                    'pluginOptions' => [
                                        'depends'=>['generation_id'],
                                        'placeholder'=>'Выберите поколение',
                                        'url'=>Url::to(['/chip-engine/json-list']),
                                    ],
                                ]); ?>
                            </div>
                            <div class="col-sm-12 col-xl-2 col-md-2">
                                <?= $form->field($model, 'ecu_id')->widget(Select2::classname(), [
//                                    'type' => DepDrop::TYPE_SELECT2,
                                    'language' => 'ru',
                                'data' => ArrayHelper::map(common\models\ChipEcuDict::find()->orderBy('title')->all(),'id', 'title'),
                                    'options' => [
                                        'placeholder' => 'Выберите',
                                    ],
//                                    'select2Options' => ['pluginOptions' => []],
                                    'pluginOptions' => [
                                            'allowClear' => true,
//                                        'depends'=>['engine_id'],
                                        'placeholder'=>'Выберите Двигатель',
//                                        'url'=>Url::to(['/chip-ecu/json-list']),
                                    ],
                                ]); ?>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 col-xl-2 col-md-2">
                                <div class="form-group">
                                    <label class="control-label" for="chipecu-brand_title">Brand title</label>
                                    <input type="text" id="chipecu-brand_title" class="form-control" name="brand_title">
                                    <p class="help-block help-block-error"></p>
                                </div>
                            </div>
                            <div class="col-sm-12 col-xl-2 col-md-2">
                                <div class="form-group">
                                    <label class="control-label" for="chipecu-model_title">Model title</label>
                                    <input type="text" id="chipecu-model_title" class="form-control" name="model_title">
                                    <p class="help-block help-block-error"></p>
                                </div>
                            </div>
                            <div class="col-sm-12 col-xl-2 col-md-2">
                                <div class="form-group">
                                    <label class="control-label" for="chipecu-generation_title">Generation title</label>
                                    <input type="text" id="chipecu-generation_title" class="form-control" name="generation_title">
                                    <p class="help-block help-block-error"></p>
                                </div>
                            </div>
                            <div class="col-sm-12 col-xl-2 col-md-2">
                                <div class="form-group">
                                    <label class="control-label" for="chipecu-engine_title">Engine title</label>
                                    <input type="text" id="chipecu-engine_title" class="form-control" name="engine_title">
                                    <p class="help-block help-block-error"></p>
                                </div>
                            </div>
                            <div class="col-sm-12 col-xl-2 col-md-2">
                                <div class="form-group">
                                    <label class="control-label" for="chipecu-ecu_title">ECU title</label>
                                    <input type="text" id="chipecu-ecu_title" class="form-control" name="ecu_title">
                                    <p class="help-block help-block-error"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 col-xl-6 col-md-6 m-b-30">
                                <div class="card-header">
                                    <h4><?= Yii::t('backend', 'Edit tuning additions')?></h4>
                                </div>
                                <div class="card-block">
                                    <?=$this->render('/partial/ecu_additions', ['model' => $model, 'viewType' => 'full'])?>
                                </div>
                            </div>
                            <div class="col-sm-12 col-xl-6 col-md-6 m-b-30">
                                <div class="card-header">
                                    <h4><?= Yii::t('backend', 'Edit tuning stages')?></h4>
                                </div>
                                <div class="card-block">
                                    <?php foreach (\common\models\ChipStages::find()->all() as $stage) { ?>
                                        <div class="row z-depth-top-0 m-b-10  p-b-10">
                                            <div class="col-sm-12 col-xl-12 col-md-12 text-center text-danger m-b-10 m-t-10">
                                                <label><h3><?=$stage->title?></h3></label>
                                            </div>
                                            <div class="col-sm-3 col-xl-3 col-md-3 m-b-10">
                                                <label><?= Yii::t('backend', 'Horse power')?></label>
                                            </div>
                                            <div class="col-sm-9 col-xl-9 col-md-9 m-b-10">
                                                <input type="text" name="ChipEcuAdditions[stages][<?=$stage->id?>][inc_hp]" value="<?= !empty($model->stagesList[$stage->id]) ? $model->stagesList[$stage->id]->inc_hp : ''?>"/>
                                            </div>
                                            <div class="col-sm-3 col-xl-3 col-md-3 m-b-10">
                                                <label><?= Yii::t('backend', 'Torque')?></label>
                                            </div>
                                            <div class="col-sm-9 col-xl-9 col-md-9 m-b-10">
                                                <input type="text" name="ChipEcuAdditions[stages][<?=$stage->id?>][inc_tork]" value="<?= !empty($model->stagesList[$stage->id]) ? $model->stagesList[$stage->id]->inc_tork : ''?>"/>
                                            </div>
                                            <div class="col-sm-12 col-xl-3 col-md-3 m-b-10">
                                                <br><label>Описание</label>
                                            </div>
                                            <div class="col-sm-12 col-xl-9 col-md-9 m-b-10">
                                                <textarea rows="3" cols="45" name="ChipEcuAdditions[stages][<?=$stage->id?>][comment]"><?=!empty($model->stagesList[$stage->id]) ? $model->stagesList[$stage->id]->comment : ''?></textarea>
                                            </div>
                                            <div class="col-sm-3 col-xl-3 col-md-3">
                                                <label>Цена</label>
                                            </div>
                                            <div class="col-sm-9 col-xl-9 col-md-9">
                                                <input type="text" name="ChipEcuAdditions[stages][<?=$stage->id?>][price]" value="<?=!empty($model->stagesList[$stage->id]) ? $model->stagesList[$stage->id]->price : ''?>"/>
                                            </div>
                                        </div>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>
