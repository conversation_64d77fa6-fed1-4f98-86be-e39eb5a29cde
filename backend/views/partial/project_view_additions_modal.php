<?php

use common\models\ChipAddition;
use common\models\ChipBrand;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use common\models\ChipStages;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $additionsDataArray array */
/* @var $stagesDict array */
/* @var $stagesEcuDataArray array */
//echo '<pre>';
//print_r($additionsDataArray);
//die;
?>
<div class="modal fade" id="change_additions_modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"><?=Yii::t('backend', 'Tuning Options')?></h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?php $form = ActiveForm::begin([
                    'id'                   => 'change_additions',
                    'options'              => ['accept-charset'=>'utf-8', 'method' => 'post'],
                    'enableAjaxValidation' => true,
                ]); ?>
                <input type="hidden" name="id" value="<?=$model->id?>">
                    <div class="row">
                        <?php foreach (ChipAddition::find()->all() as $addition) {

                            if (count($additionsDataArray) > 0 && !isset($additionsDataArray[$addition->id])) {continue;}
                            ?>
                                <div class="col-sm-12 col-xl-12 col-md-12 m-b-5 p-b-0 sub-title">
                                    <div class="col-sm-6 col-xl-6 col-md-6">
                                        <label for="addit_pop_<?=$addition->id?>"><?=$addition->title?></label>
                                    </div>
                                    <div class="col-sm-3 col-xl-3 col-md-3">
                                        <?=!$addition->comment ? '' : ('<i class="fa fa-exclamation-triangle mytooltip text-warning fa-x2"><span class="tooltip-content5"><span class="tooltip-text3 card card-border-warning"><span class="tooltip-inner2">'.$addition->comment.'</span></span></span></i>')?>
                                    </div>
                                    <div class="col-sm-3 col-xl-3 col-md-3">
                                        <label for="addit_pop_<?=$addition->id?>">
                                            <input
                                                    type="checkbox"
                                                    <?=ArrayHelper::isIn($addition->id, $model->ProjectAdditionsIdArray) ? 'checked' : ''?>
                                                    name="additions[<?=$addition->id?>][addition_id]"
                                                    id="addit_pop_<?=$addition->id?>"
                                                    value="<?=$addition->id?>"
                                                    data-title="<?=$addition->title?>"
                                                    class="js-switch additions_param_change_box"/></label>
                                    </div>
                                </div>
                        <?php } ?>
                    </div>
                    <?php ActiveForm::end(); ?>
            </div>
            <div class="modal-footer">
                <button type="cancel" class="btn btn-default waves-effect " data-dismiss="modal"><?=Yii::t('backend', 'Close')?></button>
                <button type="button" class="btn btn-primary waves-effect waves-light change_additions_btn" data-dismiss="modal"><?=Yii::t('backend', 'Save options')?></button>
            </div>
        </div>
    </div>
</div>


