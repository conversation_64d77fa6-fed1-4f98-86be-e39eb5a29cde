<?php

use common\models\ChipAddition;
use common\models\ChipBrand;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\ChipEcu */
/* @var $form yii\bootstrap\ActiveForm */
/* @var $viewType string */
//echo '<pre>';
//print_r($model->quickAdditionsList);
//die;
switch ($viewType) {
    case 'quick':
        if (isset($model->quickAdditionsList)) {
            $model->additionsList = $model->quickAdditionsList;
        }
        break;
}
?>
<?php foreach (ChipAddition::find()->all() as $addition) { ?>
        <div class="z-depth-top-0 m-b-10 p-t-10 p-b-10 <?=$viewType=='full' ? ' col-sm-4 col-xl-4 col-md-4 ' : ' row '?>">
            <div class="row col-sm-12 col-xl-12 col-md-12">
                <div class="col-sm-3 col-xl-3 col-md-3 m-b-10">
                    <?= Yii::t('backend', 'On')?>/<?= Yii::t('backend', 'Off')?>
                    <input type="checkbox"  name="ChipEcuAdditions[additions][<?=$addition->id?>][addition_id]" id="addit_<?=$addition->id?>" value="<?=$addition->id?>" class="js-switch" <?= !empty($model->additionsList[$addition->id]) ? 'checked' : ''?>/>
                </div>
                <div class="col-sm-9 col-xl-9 col-md-9 m-b-10">
                    <label for="addit_<?=$addition->id?>" class="color-warning"><h3><?=$addition->title?></h3></label>
                </div>
            </div>
            <div class="row col-sm-12 col-xl-12 col-md-12">
                <div class="col-sm-12 col-xl-3 col-md-3 m-b-10">
                    <br><label for="addit_<?=$addition->id?>">Описание</label>
                </div>
                <div class="col-sm-12 col-xl-9 col-md-9 m-b-10">
                    <textarea rows="2" cols="45" name="ChipEcuAdditions[additions][<?=$addition->id?>][comment]"><?= !empty($model->additionsList[$addition->id]->comment) ? $model->additionsList[$addition->id]->comment : $addition->comment?></textarea>
                </div>
            </div>
            <div class="row col-sm-12 col-xl-12 col-md-12">
                <div class="col-sm-3 col-xl-3 col-md-3 m-b-10">
                    <label for="addit_<?=$addition->id?>">Цена</label>
                </div>
                <div class="col-sm-9 col-xl-9 col-md-9 m-b-10">
                    <input type="text" name="ChipEcuAdditions[additions][<?=$addition->id?>][price]" value="<?= !empty($model->additionsList[$addition->id]->price) ? $model->additionsList[$addition->id]->price : $addition->price?>"/>
                </div>
            </div>
        </div>
<?php } ?>
