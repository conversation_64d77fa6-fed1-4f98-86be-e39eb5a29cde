<?php

use common\models\ChipAddition;
use common\models\ChipBrand;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use common\models\ChipStages;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $additionsDataArray array */
/* @var $stagesDict array */
/* @var $stagesEcuDataArray array */
//echo '<pre>';
//print_r($model->projectOptions);
//die;
?>
<div class="row">
    <div class="col-md-6 col-lg-6 col-sm-12 stage-block">
        <h6 class="sub-title text-center p-t-5">
            <span class="btn btn-link ms-color project-stage-change"
                  data-id="<?=$model->id?>" data-toggle="modal" data-target="#change_stage_modal"><?=Yii::t('backend',
                    'Change stage')?> <i class="fa fa-gears"></i></span>
        </h6>
        <div class="col-md-12 col-lg-12 col-sm-12 stage-empty hidden">
            <div class="col-md-3 col-lg-3 col-sm-3 text-dark text-left">
                <span class="label label-danger f-12">No tuning required</span>
            </div>
            <div class="col-md-4 col-lg-4 col-sm-4 text-dark text-center lead">
            </div>
            <div class="col-md-1 col-lg-1 col-sm-1">
            </div>
            <div class="col-md-2 col-lg-2 col-sm-2">
            </div>
            <div class="col-md-2 col-lg-2 col-sm-2">
            </div>
        </div>
        <?php if (!empty($model->stage)) {?>
        <div class="col-md-12 col-lg-12 col-sm-12 stage stage-<?=$model->id?> p-r-0 p-l-0 p-b-15 sub-title">
            <div class="col-md-12 col-lg-5 col-sm-12 text-light text-left p-0">
                <?= !empty($model->stage) ?
                    $model->stage->title :
                    '<span class="text-danger f-12">No tuning required</span>';
                ?>
            </div>
            <div class="col-md-12 col-lg-7 col-sm-12 text-center p-0 f-12">
                <?= !empty($model->stage) ?
                    (int)$model->projectStage->inc_hp
                    . ' '
                    . Yii::t('backend', 'HP')
                    . '/'
                    . (int)$model->projectStage->inc_tork
                    . ' '
                    . Yii::t('backend', 'NM'). '' : '' ; ?>
            </div>
            <?php if (!empty($model->projectStage) && (!empty($model->projectStage->comment))) { ?>
                <div class="col-md-12 col-lg-1 col-sm-12 p-0 f-20 exclamation-block">
                    <?= '<i class="fa fa-exclamation-triangle mytooltip text-warning"><span class="tooltip-content5"><span class="tooltip-text3 card"><span class="tooltip-inner2">'.$model->projectStage->comment.'</span></span></span></i>';?>
                </div>
            <?php } ?>
        </div>
        <?php } else { ?>
        <div class="col-md-12 col-lg-12 col-sm-12 stage text-center stage-<?=$model->id?> p-r-0 p-l-0 p-b-15 sub-title">
<span class="text-danger f-12"><?=Yii::t('backend', 'No tuning required')?></span>
        </div>
         <?php } ?>
   </div>
    <div class="col-md-6 col-lg-6 col-sm-12">
        <h6 class="sub-title text-center p-t-5">
            <span class="btn btn-link ms-color project-options-change" data-id="<?=$model->id?>" data-toggle="modal" data-target="#change_additions_modal"><?=Yii::t('backend', 'Change options')?> <i class="fa fa-gears"></i></span>
        </h6>
            <?php if (!empty($model->projectOptions)) { ?>
                <?php foreach ($model->projectOptions as $option) {?>
                    <div class="col-md-12 col-lg-12 col-sm-12 p-r-0 p-l-0 p-b-15 sub-title">
                            <div class="col-md-12 col-lg-10 col-sm-12 text-dark p-0 f-12">
                                <span id="span_<?=$option->id?>" class="text-light"><?=$option->addition->title?></span>
                            </div>
                            <div class="col-md-12 col-lg-2 col-sm-12 p-0 f-12 exclamation-block">
                                <?=(isset($option->ecuAddition) && !empty(trim($option->ecuAddition->comment))) ? ('<i class="fa fa-exclamation-triangle mytooltip text-warning"><span class="tooltip-content5"><span class="tooltip-text3 card"><span class="tooltip-inner2">'.$option->ecuAddition->comment.'</span></span></span></i>') : ''?>
                            </div>
                    </div>
                <?php } ?>
            <?php } else { ?>
                <div class="col-md-12 col-lg-12 col-sm-12 stage text-center stage-<?=$model->id?> p-r-0 p-l-0 p-b-15 sub-title">
                    <span class="text-danger f-12"><?=Yii::t('backend', 'No options required')?></span>
                </div>
            <?php } ?>
    </div>
</div>
<?= $this->render('/partial/project_view_additions_modal', ['model' => $model, 'additionsDataArray' => $additionsDataArray]);?>



