<?php
/**
 * Eugine Terentev <<EMAIL>>
 * @var $this \yii\web\View
 * @var $model \common\models\TimelineEvent
 * @var $dataProvider \yii\data\ActiveDataProvider
 */

use yii\helpers\Html;
use yii\helpers\Url;

$this->title = Yii::t('backend', 'Articles');
$icons = [
    'user'=>'<i class="fa fa-user bg-blue"></i>'
];
$this->registerJs("
    // установка бэкграунда
    $('#pcoded').attr('sidebar-img-type','auto7');
");

?>
<?php \yii\widgets\Pjax::begin() ?>
<div class="row">

    <div class="col-md-12 col-lg-12 col-sm-12">
        <div class="col-xl-12 col-md-12 col-sm-12">
            <div class="">
                <div class="card-header project-files-head">
                    <h5 class="card-header-text"><i class="icofont icofont-ui-note m-r-10"></i> <?php echo Yii::t('backend', 'News') ?></h5>
                </div>
                <div class="card-block task-details">
                    <?php if ($dataProvider->count > 0): ?>
                        <?php foreach($dataProvider->getModels() as $model): ?>
                            <hr/>
                            <div class="article-item row">
                                <div class="col-xs-12 col-xl-12 col-md-12 col-sm-12">
                                    <h2 class="article-title m-b-25">
                                        <?php echo Html::a($model->title, ['/content/article/view', 'slug'=>$model->slug]) ?>
                                    </h2>
                                    <div class="col-xl-10 col-md-10 col-sm-10 text-left p-l-0" style="line-height: 3;">
                                        <?php echo Yii::$app->formatter->asDatetime($model->created_at, "d-MM-y") ?>
                                    </div>
                                    <div class="col-xl-2 col-md-2 col-sm-2 text-right">
                                        <span class="text-dark btn btn-grd-primary"><?php echo Html::a(Yii::t('backend', 'Read more'), ['/content/article/view', 'slug' => $model->slug]) ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-12 col-md-12 col-sm-12 sub-title"></div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <span class="text-dark"><?php echo Yii::t('backend', 'No articles yet') ?></span>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-12 text-center">
        <?php echo \yii\widgets\LinkPager::widget([
            'pagination'=>$dataProvider->pagination,
            'options' => ['class' => 'pagination']
        ]) ?>
    </div>

    <?php \yii\widgets\Pjax::end() ?>

