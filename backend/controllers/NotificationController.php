<?php

namespace backend\controllers;

use common\chip\notification\NotificationFacade;
use common\models\notification\NotificationChannel;
use common\models\notification\NotificationDelivery;
use Yii;
use yii\filters\AccessControl;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\Response;

/**
 * Контроллер для работы с уведомлениями
 */
class NotificationController extends Controller
{
    /**
     * @var NotificationFacade
     */
    private $notificationFacade;
    
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'mark-as-read' => ['post'],
                    'mark-as-clicked' => ['post'],
                ],
            ],
        ];
    }
    
    /**
     * {@inheritdoc}
     */
    public function __construct($id, $module, NotificationFacade $notificationFacade, $config = [])
    {
        $this->notificationFacade = $notificationFacade;
        parent::__construct($id, $module, $config);
    }
    
    /**
     * Получить UI-уведомления для текущего пользователя
     *
     * @return Response
     */
    public function actionGetUiNotifications()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $userId = Yii::$app->user->id;
        $deliveries = $this->notificationFacade->getUnreadNotificationsForUser($userId, NotificationChannel::CHANNEL_UI_POPUP);
        
        $notifications = [];
        
        foreach ($deliveries as $delivery) {
            if (!$delivery->canDisplay()) {
                continue;
            }
            
            $notification = $delivery->notification;
            
            $notifications[] = [
                'id' => $delivery->id,
                'notification_id' => $notification->id,
                'type' => $notification->type,
                'subject' => $notification->subject,
                'content' => $notification->content,
                'importance' => $notification->importance,
                'display_location' => $delivery->display_location,
                'show_until_clicked' => $delivery->show_until_clicked,
                'created_at' => $notification->created_at,
            ];
            
            // Отмечаем как доставленное
            $delivery->markAsDelivered();
        }
        
        return [
            'success' => true,
            'notifications' => $notifications,
        ];
    }
    
    /**
     * Отметить уведомление как прочитанное
     *
     * @return Response
     */
    public function actionMarkAsRead()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $id = Yii::$app->request->post('id');
        
        if (!$id) {
            return [
                'success' => false,
                'message' => 'ID не указан',
            ];
        }
        
        $delivery = NotificationDelivery::findOne($id);
        
        if (!$delivery) {
            return [
                'success' => false,
                'message' => 'Уведомление не найдено',
            ];
        }
        
        // Проверяем, что уведомление принадлежит текущему пользователю
        if ($delivery->recipient_type === NotificationDelivery::RECIPIENT_TYPE_USER && $delivery->recipient_id != Yii::$app->user->id) {
            return [
                'success' => false,
                'message' => 'Доступ запрещен',
            ];
        }
        
        $delivery->markAsRead();
        
        return [
            'success' => true,
            'message' => 'Уведомление отмечено как прочитанное',
        ];
    }
    
    /**
     * Отметить уведомление как кликнутое
     *
     * @return Response
     */
    public function actionMarkAsClicked()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $id = Yii::$app->request->post('id');
        
        if (!$id) {
            return [
                'success' => false,
                'message' => 'ID не указан',
            ];
        }
        
        $delivery = NotificationDelivery::findOne($id);
        
        if (!$delivery) {
            return [
                'success' => false,
                'message' => 'Уведомление не найдено',
            ];
        }
        
        // Проверяем, что уведомление принадлежит текущему пользователю
        if ($delivery->recipient_type === NotificationDelivery::RECIPIENT_TYPE_USER && $delivery->recipient_id != Yii::$app->user->id) {
            return [
                'success' => false,
                'message' => 'Доступ запрещен',
            ];
        }
        
        $delivery->markAsClicked();
        
        return [
            'success' => true,
            'message' => 'Уведомление отмечено как кликнутое',
        ];
    }
    
    /**
     * Получить уведомления для проекта
     *
     * @param int $projectId ID проекта
     * @return Response
     */
    public function actionGetProjectNotifications($projectId)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $deliveries = $this->notificationFacade->getNotificationsForProject($projectId);
        
        $notifications = [];
        
        foreach ($deliveries as $delivery) {
            $notification = $delivery->notification;
            
            $notifications[] = [
                'id' => $delivery->id,
                'notification_id' => $notification->id,
                'type' => $notification->type,
                'subject' => $notification->subject,
                'content' => $notification->content,
                'importance' => $notification->importance,
                'created_at' => $notification->created_at,
            ];
        }
        
        return [
            'success' => true,
            'notifications' => $notifications,
        ];
    }
}
