$schema: https://raw.githubusercontent.com/context-hub/generator/refs/heads/main/json-schema.json

documents:
  - 
    title: Core Database Models
    description: Primary database models and their relationships
    outputPath: architecture/models.md
    overwrite: true
    sources:
      - 
        type: file
        sourcePaths:
          - common/models/User.php
          - common/models/Projects.php
          - common/models/ProjectFiles.php
          - common/models/ProjectOptions.php
          - common/models/ChipModel.php
          - common/models/ChipBrand.php
          - common/models/ChipVehicle.php
          - common/models/ChipGeneration.php
          - common/models/ChipEngine.php

  - 
    title: Chip Tuning Models
    description: Chip tuning specific database models
    outputPath: architecture/chip-models.md
    overwrite: true
    sources:
      - 
        type: file
        sourcePaths:
          - common/models/ChipEcu.php
          - common/models/ChipEcuDict.php
          - common/models/ChipEcuStages.php
          - common/models/ChipStages.php
          - common/models/ChipAddition.php
          - common/models/ChipEcuAdditions.php
          - common/models/ChipEcuAdditionsLink.php
          - common/models/ChipReadmethod.php
          - common/models/ChipGearbox.php

  - 
    title: Query Models
    description: Database query models and ActiveQuery extensions
    outputPath: architecture/queries.md
    overwrite: true
    sources:
      - 
        type: file
        sourcePaths:
          - common/models/query/BaseModelQuery.php
          - common/models/query/UserQuery.php
          - common/models/query/ProjectsQuery.php
          - common/models/query/ProjectFilesQuery.php
          - common/models/query/ChipEcuQuery.php
          - common/models/query/ChipModelQuery.php
          - common/models/query/ChipVehicleQuery.php
          - common/models/query/ProjectMessagesQuery.php
