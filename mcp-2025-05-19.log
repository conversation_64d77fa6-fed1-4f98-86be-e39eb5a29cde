[2025-05-19T12:25:27.596998+00:00] mcp.ALERT: Fatal <PERSON> (E_ERROR): Allowed memory size of 134217728 bytes exhausted (tried to allocate 20480 bytes) {"code":1,"message":"Allowed memory size of 134217728 bytes exhausted (tried to allocate 20480 bytes)","file":"phar://F:/2025_04/ctx.exe/vendor/symfony/finder/Iterator/RecursiveDirectoryIterator.php","line":72,"trace":null} {"tags":["ExceptionHandler"]}
[2025-05-19T12:34:23.745225+00:00] mcp.ERROR: Error listing directory {"path":"D:\\000\\chiptuning","error":"str_replace(): Argument #3 ($subject) must be of type array|string, false given","trace":"#0 phar://F:/2025_04/ctx.exe/src/McpServer/Action/Tools/Filesystem/DirectoryListAction.php(203): str_replace('D:\\\\000\\\\chiptuni...', '', false)\n#1 [internal function]: Butschster\\ContextGenerator\\McpServer\\Action\\Tools\\Filesystem\\DirectoryListAction->__invoke(Object(Laminas\\Diactoros\\ServerRequest))\n#2 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Internal/Invoker.php(69): ReflectionMethod->invokeArgs(Object(Butschster\\ContextGenerator\\McpServer\\Action\\Tools\\Filesystem\\DirectoryListAction), Array)\n#3 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(284): Spiral\\Core\\Internal\\Invoker->invoke(Array, Array)\n#4 phar://F:/2025_04/ctx.exe/src/McpServer/Routing/ActionCaller.php(30): Spiral\\Core\\Container->invoke(Array)\n#5 [internal function]: Butschster\\ContextGenerator\\McpServer\\Routing\\ActionCaller->Butschster\\ContextGenerator\\McpServer\\Routing\\{closure}(Object(Spiral\\Core\\Container))\n#6 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Internal/Invoker.php(87): ReflectionFunction->invokeArgs(Array)\n#7 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(284): Spiral\\Core\\Internal\\Invoker->invoke(Object(Closure), Array)\n#8 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(410): Spiral\\Core\\Container->invoke(Object(Closure))\n#9 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/ContainerScope.php(45): Spiral\\Core\\Container::Spiral\\Core\\{closure}(Object(Spiral\\Core\\Container))\n#10 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(405): Spiral\\Core\\ContainerScope::runScope(Object(Spiral\\Core\\Container), Object(Closure))\n#11 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(171): Spiral\\Core\\Container->runIsolatedScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#12 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Internal/Proxy.php(60) : eval()'d code(12): Spiral\\Core\\Container->runScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#13 phar://F:/2025_04/ctx.exe/src/McpServer/Routing/ActionCaller.php(23): Spiral\\Core\\ScopeInterface SCOPED PROXY->runScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#14 phar://F:/2025_04/ctx.exe/src/McpServer/Routing/McpResponseStrategy.php(31): Butschster\\ContextGenerator\\McpServer\\Routing\\ActionCaller->__invoke(Object(Laminas\\Diactoros\\ServerRequest), Array)\n#15 phar://F:/2025_04/ctx.exe/vendor/league/route/src/Route.php(124): Butschster\\ContextGenerator\\McpServer\\Routing\\McpResponseStrategy->invokeRouteCallable(Object(League\\Route\\Route), Object(Laminas\\Diactoros\\ServerRequest))\n#16 phar://F:/2025_04/ctx.exe/vendor/league/route/src/Dispatcher.php(59): League\\Route\\Route->process(Object(Laminas\\Diactoros\\ServerRequest), Object(League\\Route\\Dispatcher))\n#17 phar://F:/2025_04/ctx.exe/vendor/league/route/src/Strategy/ApplicationStrategy.php(37): League\\Route\\Dispatcher->handle(Object(Laminas\\Diactoros\\ServerRequest))\n#18 phar://F:/2025_04/ctx.exe/vendor/league/route/src/Dispatcher.php(59): Psr\\Http\\Server\\MiddlewareInterface@anonymous->process(Object(Laminas\\Diactoros\\ServerRequest), Object(League\\Route\\Dispatcher))\n#19 phar://F:/2025_04/ctx.exe/vendor/league/route/src/Dispatcher.php(53): League\\Route\\Dispatcher->handle(Object(Laminas\\Diactoros\\ServerRequest))\n#20 phar://F:/2025_04/ctx.exe/vendor/league/route/src/Router.php(97): League\\Route\\Dispatcher->dispatchRequest(Object(Laminas\\Diactoros\\ServerRequest))\n#21 phar://F:/2025_04/ctx.exe/src/McpServer/Server.php(133): League\\Route\\Router->dispatch(Object(Laminas\\Diactoros\\ServerRequest))\n#22 phar://F:/2025_04/ctx.exe/src/McpServer/Server.php(83): Butschster\\ContextGenerator\\McpServer\\Server->handleToolCall(Object(Mcp\\Types\\CallToolRequestParams))\n#23 phar://F:/2025_04/ctx.exe/vendor/logiscape/mcp-sdk-php/src/Server/ServerSession.php(196): Butschster\\ContextGenerator\\McpServer\\Server->Butschster\\ContextGenerator\\McpServer\\{closure}(Object(Mcp\\Types\\CallToolRequestParams))\n#24 phar://F:/2025_04/ctx.exe/vendor/logiscape/mcp-sdk-php/src/Shared/BaseSession.php(288): Mcp\\Server\\ServerSession->handleRequest(Object(Mcp\\Shared\\RequestResponder))\n#25 phar://F:/2025_04/ctx.exe/vendor/logiscape/mcp-sdk-php/src/Server/ServerSession.php(386): Mcp\\Shared\\BaseSession->handleIncomingMessage(Object(Mcp\\Types\\JsonRpcMessage))\n#26 phar://F:/2025_04/ctx.exe/vendor/logiscape/mcp-sdk-php/src/Shared/BaseSession.php(81): Mcp\\Server\\ServerSession->startMessageProcessing()\n#27 phar://F:/2025_04/ctx.exe/vendor/logiscape/mcp-sdk-php/src/Server/ServerSession.php(101): Mcp\\Shared\\BaseSession->initialize()\n#28 phar://F:/2025_04/ctx.exe/vendor/logiscape/mcp-sdk-php/src/Server/ServerRunner.php(85): Mcp\\Server\\ServerSession->start()\n#29 phar://F:/2025_04/ctx.exe/src/McpServer/Server.php(45): Mcp\\Server\\ServerRunner->run()\n#30 phar://F:/2025_04/ctx.exe/src/McpServer/ServerRunner.php(59): Butschster\\ContextGenerator\\McpServer\\Server->run('Context Generat...')\n#31 [internal function]: Butschster\\ContextGenerator\\McpServer\\ServerRunner->Butschster\\ContextGenerator\\McpServer\\{closure}(Object(Butschster\\ContextGenerator\\McpServer\\Routing\\RouteRegistrar), Object(Butschster\\ContextGenerator\\McpServer\\Registry\\McpItemsRegistry), Object(Butschster\\ContextGenerator\\Application\\Logger\\FileLogger))\n#32 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Internal/Invoker.php(87): ReflectionFunction->invokeArgs(Array)\n#33 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(284): Spiral\\Core\\Internal\\Invoker->invoke(Object(Closure), Array)\n#34 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(410): Spiral\\Core\\Container->invoke(Object(Closure))\n#35 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/ContainerScope.php(45): Spiral\\Core\\Container::Spiral\\Core\\{closure}(Object(Spiral\\Core\\Container))\n#36 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(405): Spiral\\Core\\ContainerScope::runScope(Object(Spiral\\Core\\Container), Object(Closure))\n#37 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(171): Spiral\\Core\\Container->runIsolatedScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#38 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Internal/Proxy.php(60) : eval()'d code(12): Spiral\\Core\\Container->runScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#39 phar://F:/2025_04/ctx.exe/src/McpServer/ServerRunner.php(40): Spiral\\Core\\ScopeInterface SCOPED PROXY->runScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#40 phar://F:/2025_04/ctx.exe/src/Console/MCPServerCommand.php(116): Butschster\\ContextGenerator\\McpServer\\ServerRunner->run('Context Generat...')\n#41 [internal function]: Butschster\\ContextGenerator\\Console\\MCPServerCommand::Butschster\\ContextGenerator\\Console\\{closure}(Object(Butschster\\ContextGenerator\\McpServer\\ServerRunner))\n#42 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Internal/Invoker.php(87): ReflectionFunction->invokeArgs(Array)\n#43 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(284): Spiral\\Core\\Internal\\Invoker->invoke(Object(Closure), Array)\n#44 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(410): Spiral\\Core\\Container->invoke(Object(Closure))\n#45 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/ContainerScope.php(45): Spiral\\Core\\Container::Spiral\\Core\\{closure}(Object(Spiral\\Core\\Container))\n#46 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(405): Spiral\\Core\\ContainerScope::runScope(Object(Spiral\\Core\\Container), Object(Closure))\n#47 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(171): Spiral\\Core\\Container->runIsolatedScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#48 phar://F:/2025_04/ctx.exe/src/Console/MCPServerCommand.php(105): Spiral\\Core\\Container->runScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#49 [internal function]: Butschster\\ContextGenerator\\Console\\MCPServerCommand::Butschster\\ContextGenerator\\Console\\{closure}(Object(Spiral\\Core\\Container), Object(Butschster\\ContextGenerator\\Config\\ConfigurationProvider))\n#50 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Internal/Invoker.php(87): ReflectionFunction->invokeArgs(Array)\n#51 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(284): Spiral\\Core\\Internal\\Invoker->invoke(Object(Closure), Array)\n#52 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(410): Spiral\\Core\\Container->invoke(Object(Closure))\n#53 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/ContainerScope.php(45): Spiral\\Core\\Container::Spiral\\Core\\{closure}(Object(Spiral\\Core\\Container))\n#54 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(405): Spiral\\Core\\ContainerScope::runScope(Object(Spiral\\Core\\Container), Object(Closure))\n#55 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(171): Spiral\\Core\\Container->runIsolatedScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#56 phar://F:/2025_04/ctx.exe/src/Console/MCPServerCommand.php(70): Spiral\\Core\\Container->runScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#57 [internal function]: Butschster\\ContextGenerator\\Console\\MCPServerCommand->__invoke(Object(Spiral\\Core\\Container), Object(Butschster\\ContextGenerator\\Directories), Object(Butschster\\ContextGenerator\\Application\\Application))\n#58 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Internal/Invoker.php(69): ReflectionMethod->invokeArgs(Object(Butschster\\ContextGenerator\\Console\\MCPServerCommand), Array)\n#59 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(284): Spiral\\Core\\Internal\\Invoker->invoke(Array, Array)\n#60 phar://F:/2025_04/ctx.exe/vendor/spiral/console/src/CommandCore.php(35): Spiral\\Core\\Container->invoke(Array, Array)\n#61 phar://F:/2025_04/ctx.exe/vendor/spiral/hmvc/src/InterceptorPipeline.php(115): Spiral\\Console\\CommandCore->handle(Object(Spiral\\Interceptors\\Context\\CallContext))\n#62 phar://F:/2025_04/ctx.exe/vendor/spiral/hmvc/src/InterceptorPipeline.php(75): Spiral\\Core\\InterceptorPipeline->handle(Object(Spiral\\Interceptors\\Context\\CallContext))\n#63 phar://F:/2025_04/ctx.exe/vendor/spiral/console/src/Interceptor/AttributeInterceptor.php(26): Spiral\\Core\\InterceptorPipeline->callAction('Butschster\\\\Cont...', '__invoke', Array)\n#64 phar://F:/2025_04/ctx.exe/vendor/spiral/hmvc/src/InterceptorPipeline.php(110): Spiral\\Console\\Interceptor\\AttributeInterceptor->process('Butschster\\\\Cont...', '__invoke', Array, Object(Spiral\\Core\\InterceptorPipeline))\n#65 phar://F:/2025_04/ctx.exe/vendor/spiral/console/src/Command.php(120): Spiral\\Core\\InterceptorPipeline->handle(Object(Spiral\\Interceptors\\Context\\CallContext))\n#66 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(411): Spiral\\Console\\Command->Spiral\\Console\\{closure}(Object(Spiral\\Core\\Container))\n#67 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/ContainerScope.php(45): Spiral\\Core\\Container::Spiral\\Core\\{closure}(Object(Spiral\\Core\\Container))\n#68 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(405): Spiral\\Core\\ContainerScope::runScope(Object(Spiral\\Core\\Container), Object(Closure))\n#69 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(171): Spiral\\Core\\Container->runIsolatedScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#70 phar://F:/2025_04/ctx.exe/vendor/spiral/console/src/Command.php(106): Spiral\\Core\\Container->runScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#71 phar://F:/2025_04/ctx.exe/src/Console/BaseCommand.php(55): Spiral\\Console\\Command->execute(Object(Spiral\\Console\\InputProxy), Object(Symfony\\Component\\Console\\Style\\SymfonyStyle))\n#72 [internal function]: Butschster\\ContextGenerator\\Console\\BaseCommand->Butschster\\ContextGenerator\\Console\\{closure}()\n#73 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Internal/Invoker.php(87): ReflectionFunction->invokeArgs(Array)\n#74 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(284): Spiral\\Core\\Internal\\Invoker->invoke(Object(Closure), Array)\n#75 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(410): Spiral\\Core\\Container->invoke(Object(Closure))\n#76 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/ContainerScope.php(45): Spiral\\Core\\Container::Spiral\\Core\\{closure}(Object(Spiral\\Core\\Container))\n#77 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(405): Spiral\\Core\\ContainerScope::runScope(Object(Spiral\\Core\\Container), Object(Closure))\n#78 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(171): Spiral\\Core\\Container->runIsolatedScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#79 phar://F:/2025_04/ctx.exe/src/Console/BaseCommand.php(48): Spiral\\Core\\Container->runScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#80 phar://F:/2025_04/ctx.exe/vendor/symfony/console/Command/Command.php(279): Butschster\\ContextGenerator\\Console\\BaseCommand->execute(Object(Spiral\\Console\\InputProxy), Object(Symfony\\Component\\Console\\Style\\SymfonyStyle))\n#81 phar://F:/2025_04/ctx.exe/vendor/symfony/console/Application.php(1076): Symfony\\Component\\Console\\Command\\Command->run(Object(Spiral\\Console\\InputProxy), Object(Symfony\\Component\\Console\\Style\\SymfonyStyle))\n#82 phar://F:/2025_04/ctx.exe/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Butschster\\ContextGenerator\\Console\\MCPServerCommand), Object(Spiral\\Console\\InputProxy), Object(Symfony\\Component\\Console\\Style\\SymfonyStyle))\n#83 phar://F:/2025_04/ctx.exe/vendor/spiral/console/src/Console.php(86): Symfony\\Component\\Console\\Application->doRun(Object(Spiral\\Console\\InputProxy), Object(Symfony\\Component\\Console\\Style\\SymfonyStyle))\n#84 [internal function]: Spiral\\Console\\Console->Spiral\\Console\\{closure}()\n#85 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Internal/Invoker.php(87): ReflectionFunction->invokeArgs(Array)\n#86 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(284): Spiral\\Core\\Internal\\Invoker->invoke(Object(Closure), Array)\n#87 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(410): Spiral\\Core\\Container->invoke(Object(Closure))\n#88 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/ContainerScope.php(45): Spiral\\Core\\Container::Spiral\\Core\\{closure}(Object(Spiral\\Core\\Container))\n#89 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(405): Spiral\\Core\\ContainerScope::runScope(Object(Spiral\\Core\\Container), Object(Closure))\n#90 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(171): Spiral\\Core\\Container->runIsolatedScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#91 phar://F:/2025_04/ctx.exe/vendor/spiral/console/src/Console.php(79): Spiral\\Core\\Container->runScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#92 phar://F:/2025_04/ctx.exe/src/Application/Dispatcher/ConsoleDispatcher.php(48): Spiral\\Console\\Console->run('server', Object(Spiral\\Console\\InputProxy), Object(Symfony\\Component\\Console\\Style\\SymfonyStyle))\n#93 [internal function]: Butschster\\ContextGenerator\\Application\\Dispatcher\\ConsoleDispatcher->Butschster\\ContextGenerator\\Application\\Dispatcher\\{closure}(Object(Spiral\\Core\\Container))\n#94 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Internal/Invoker.php(87): ReflectionFunction->invokeArgs(Array)\n#95 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(284): Spiral\\Core\\Internal\\Invoker->invoke(Object(Closure), Array)\n#96 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(410): Spiral\\Core\\Container->invoke(Object(Closure))\n#97 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/ContainerScope.php(45): Spiral\\Core\\Container::Spiral\\Core\\{closure}(Object(Spiral\\Core\\Container))\n#98 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(405): Spiral\\Core\\ContainerScope::runScope(Object(Spiral\\Core\\Container), Object(Closure))\n#99 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(171): Spiral\\Core\\Container->runIsolatedScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#100 phar://F:/2025_04/ctx.exe/src/Application/Dispatcher/ConsoleDispatcher.php(40): Spiral\\Core\\Container->runScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#101 phar://F:/2025_04/ctx.exe/vendor/spiral/boot/src/AbstractKernel.php(307): Butschster\\ContextGenerator\\Application\\Dispatcher\\ConsoleDispatcher->serve()\n#102 [internal function]: Spiral\\Boot\\AbstractKernel::Spiral\\Boot\\{closure}(Object(Butschster\\ContextGenerator\\Application\\Dispatcher\\ConsoleDispatcher))\n#103 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Internal/Invoker.php(87): ReflectionFunction->invokeArgs(Array)\n#104 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(284): Spiral\\Core\\Internal\\Invoker->invoke(Object(Closure), Array)\n#105 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(410): Spiral\\Core\\Container->invoke(Object(Closure))\n#106 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/ContainerScope.php(45): Spiral\\Core\\Container::Spiral\\Core\\{closure}(Object(Spiral\\Core\\Container))\n#107 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(405): Spiral\\Core\\ContainerScope::runScope(Object(Spiral\\Core\\Container), Object(Closure))\n#108 phar://F:/2025_04/ctx.exe/vendor/spiral/core/src/Container.php(171): Spiral\\Core\\Container->runIsolatedScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#109 phar://F:/2025_04/ctx.exe/vendor/spiral/boot/src/AbstractKernel.php(303): Spiral\\Core\\Container->runScope(Object(Spiral\\Core\\Scope), Object(Closure))\n#110 phar://F:/2025_04/ctx.exe/app.php(110): Spiral\\Boot\\AbstractKernel->serve()\n#111 phar://F:/2025_04/ctx.exe/ctx(3): require('phar://F:/2025_...')\n#112 F:\\2025_04\\ctx.exe(13): require('phar://F:/2025_...')\n#113 {main}"} {"tags":["DirectoryListAction"]}
