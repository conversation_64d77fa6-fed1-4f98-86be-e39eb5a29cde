{"version": 1, "defects": {"tests\\unit\\ECUProjectServiceTest::testUpdateProjectStatus": 8, "tests\\unit\\ECUProjectServiceTest::testGetProjectById": 8, "tests\\unit\\ECUProjectServiceTest::testCreateProject": 8, "tests\\unit\\ECUProjectServiceTest::testProjectExists": 8, "CodeceptionTest::testCodeception": 8, "PHPUnitECUProjectServiceTest::testCreateProject": 7, "PHPUnitECUProjectServiceTest::testProjectHelperStatusConstants": 7, "tests\\unit\\AlientechAuthServiceTest::testGetAuthUrl": 8, "tests\\unit\\AlientechAuthServiceTest::testGetSecretKey": 8, "tests\\unit\\AlientechAuthServiceTest::testIsAuthorized": 8, "tests\\unit\\AlientechAuthServiceTest::testGetClientApplicationGUID": 8, "tests\\unit\\AlientechAuthServiceTest::testAuthenticate": 8, "tests\\unit\\AlientechAuthServiceTest::testGetAccessToken": 8, "tests\\unit\\AlientechApiClientTest::testCreateRequestNotAuthorized": 8, "tests\\unit\\AlientechApiClientTest::testProcessResponse": 8, "tests\\unit\\AlientechApiClientTest::testCreateRequest": 8, "tests\\unit\\ConfigurationServiceTest::testBuildConfigList": 8, "tests\\unit\\ConfigurationServiceTest::testSaveConfig": 8, "tests\\unit\\ConfigurationServiceTest::testFillConfigScript": 8, "tests\\unit\\ConfigurationServiceTest::testSearchConfigList": 8, "tests\\unit\\AlientechRequestBuilderTest::testBuildUploadModifiedFileRequest": 8, "tests\\unit\\AlientechRequestBuilderTest::testBuildDownloadFileRequest": 8, "tests\\unit\\AlientechRequestBuilderTest::testBuildGetSlotsRequest": 8, "tests\\unit\\AlientechRequestBuilderTest::testBuildReopenSlotRequest": 8, "tests\\unit\\AlientechRequestBuilderTest::testBuildCloseSlotRequest": 8, "tests\\unit\\AlientechRequestBuilderTest::testBuildDecodeRequest": 8, "tests\\unit\\AlientechRequestBuilderTest::testBuildEncodeRequest": 8, "tests\\unit\\QueueMonitoringServiceTest::testGetJobCount": 8, "tests\\unit\\QueueMonitoringServiceTest::testGetAverageJobDuration": 8, "tests\\unit\\QueueMonitoringServiceTest::testTrackJobSuccess": 8, "tests\\unit\\QueueMonitoringServiceTest::testTrackJobFailure": 8, "tests\\unit\\QueueMonitoringServiceTest::testGetFailedJobCount": 8, "tests\\unit\\QueueMonitoringServiceTest::testTrackJobStart": 8, "tests\\unit\\QueueMonitoringServiceTest::testTrackJobPush": 8, "tests\\unit\\AutopackApiClientTest::testProcessResponseError": 8, "tests\\unit\\AutopackApiClientTest::testCreateRequest": 8, "tests\\unit\\AutopackApiClientTest::testAuthenticate": 8, "tests\\unit\\AutopackApiClientTest::testProcessResponse": 8, "tests\\unit\\FileSlotServiceTest::testCloseFileSlots": 8, "tests\\unit\\FileSlotServiceTest::testHasFileSlotsForOpen": 8, "tests\\unit\\FileSlotServiceTest::testGetSlots": 8, "tests\\unit\\ECUFileServiceTest::testUpdateProjectFile": 8, "tests\\unit\\ECUFileServiceTest::testGetProjectFilesByProjectId": 8, "tests\\unit\\ECUFileServiceTest::testGetProjectFileById": 8, "tests\\unit\\ECUFileServiceTest::testSaveProjectFile": 8, "tests\\unit\\AlientechCallbackControllerTest::testActionKess3Encoded": 8, "tests\\unit\\AlientechCallbackControllerTest::testActionKess3DecodedFailure": 8, "tests\\unit\\AlientechCallbackControllerTest::testActionKess3Decoded": 8, "tests\\unit\\MonitoringServiceTest::testMeasureTime": 8, "tests\\unit\\MonitoringServiceTest::testRegisterAndDecrementGauge": 8, "tests\\unit\\MonitoringServiceTest::testRegisterAndSetGauge": 8, "tests\\unit\\MonitoringServiceTest::testRegisterAndObserveHistogram": 8, "tests\\unit\\MonitoringServiceTest::testRegisterAndObserveSummary": 8, "tests\\unit\\MonitoringServiceTest::testRegisterAndIncrementGauge": 8, "tests\\unit\\MonitoringServiceTest::testRegisterAndIncrementCounter": 8, "tests\\unit\\MonitoringControllerTest::testActionQueues": 8, "tests\\unit\\MonitoringControllerTest::testActionMetrics": 8, "tests\\unit\\MonitoringControllerTest::testActionIndex": 8, "tests\\unit\\MonitoringControllerTest::testActionPerformance": 8, "tests\\unit\\MonitoringControllerTest::testActionApi": 8, "tests\\unit\\DecodingServiceTest::testStartDecoding": 8, "tests\\unit\\DecodingServiceTest::testProcessDecodingResultWithInvalidData": 8, "tests\\unit\\DecodingServiceTest::testProcessDecodingResultWithError": 8, "tests\\unit\\DecodingServiceTest::testProcessDecodingResult": 8, "tests\\unit\\ApiMonitoringServiceTest::testGetErrorCount": 8, "tests\\unit\\ApiMonitoringServiceTest::testTrackApiRequest": 8, "tests\\unit\\ApiMonitoringServiceTest::testTrackApiTimeout": 8, "tests\\unit\\ApiMonitoringServiceTest::testGetAverageRequestDuration": 8, "tests\\unit\\ApiMonitoringServiceTest::testGetTimeoutCount": 8, "tests\\unit\\ApiMonitoringServiceTest::testTrackApiSuccess": 8, "tests\\unit\\ApiMonitoringServiceTest::testTrackApiError": 8, "tests\\unit\\QueryProfilerTest::testEnableDisableSaving": 8, "tests\\unit\\QueryProfilerTest::testRegisterMetrics": 8, "tests\\unit\\QueryProfilerTest::testSaveQuery": 8, "tests\\unit\\QueryProfilerTest::testSetThreshold": 8, "tests\\unit\\QueryProfilerTest::testEnableDisableLogging": 8, "tests\\unit\\QueryProfilerTest::testGetQueryType": 8, "tests\\unit\\QueryProfilerTest::testEnableDisable": 8, "tests\\unit\\EncodingServiceTest::testIsProjectReadyForEncoding": 8, "tests\\unit\\EncodingServiceTest::testStartEncoding": 8, "tests\\unit\\EncodingServiceTest::testIsProjectReadyForEncodingNoOriginalFiles": 8, "tests\\unit\\EncodingServiceTest::testIsProjectReadyForEncodingNoModifiedFiles": 8, "tests\\unit\\EncodingServiceTest::testIsProjectReadyForEncodingNoFreeSlots": 8, "MockCodeceptionTest::testCreateProject": 8, "tests\\functional\\MonitoringControllerTest::testMetrics": 8, "tests\\functional\\MonitoringControllerTest::testIndex": 8, "tests\\functional\\MonitoringControllerTest::testQueues": 8, "tests\\functional\\MonitoringControllerTest::testPerformance": 8, "tests\\functional\\MonitoringControllerTest::testApi": 8, "PHPUnit_ECUProjectServiceTest::testCreateProject": 7, "PHPUnit_ECUProjectServiceTest::testUpdateProjectStatus": 8, "PHPUnit_ECUProjectServiceTest::testGetProjectById": 8, "PHPUnit_ECUProjectServiceTest::testProjectExists": 8, "PHPUnit_ECUProjectServiceMockTest::testUpdateProjectStatusWithMock": 8, "PHPUnit_ECUProjectServiceMockTest::testCreateProjectWithMock": 7, "PHPUnit_ECUFileServiceMockTest::testSaveProjectFileWithMock": 7, "PHPUnit_DecodingServiceMockTest::testProcessDecodingResultWithInvalidDataMock": 8, "PHPUnit_DecodingServiceMockTest::testProcessDecodingResultWithErrorMock": 8, "PHPUnit_DecodingServiceMockTest::testProcessDecodingResultWithMock": 8, "PHPUnit_DecodingServiceMockTest::testStartDecodingWithMock": 7, "PHPUnit_ECUFileServiceTest::testUpdateProjectFile": 8, "PHPUnit_ECUFileServiceTest::testGetProjectFilesByProjectId": 8, "PHPUnit_ECUFileServiceTest::testSaveProjectFile": 8, "PHPUnit_ECUFileServiceTest::testGetProjectFileById": 8, "PHPUnit_DecodingServiceTest::testStartDecoding": 8, "PHPUnit_DecodingServiceTest::testProcessDecodingResultWithInvalidData": 8, "PHPUnit_DecodingServiceTest::testProcessDecodingResult": 8, "PHPUnit_DecodingServiceTest::testProcessDecodingResultWithError": 8, "PHPUnit_ProjectCreationServiceTest::testGetModelFormForDealer": 7, "PHPUnit_ProjectCreationServiceTest::testGetModelFormForRegularUser": 7, "PHPUnit_ProjectCreationServiceTest::testCreateProjectNull": 7, "PHPUnit_ProjectCreationServiceTest::testCreateProjectSuccess": 8, "PHPUnit_ProjectCreationServiceTest::testGetModelFormForMsDealer": 7, "PHPUnit_ProjectCreationServiceTest::testCreateProjectError": 7, "PHPUnit_ProjectOptionsServiceTest::testProcessOptionsEmpty": 8, "PHPUnit_ProjectOptionsServiceTest::testProcessOptionsWithRequest": 1, "PHPUnit_ProjectOptionsServiceTest::testProcessOptionsWithAdditions": 1, "PHPUnit_ProjectFileProcessingServiceTest::testProcessFilesWithMultipleFiles": 8, "PHPUnit_ProjectFileProcessingServiceTest::testProcessFilesWithSlaveFileForAlientech": 8, "PHPUnit_ProjectFileProcessingServiceTest::testProcessFilesWithMasterFileForAutopack": 8, "ProjectCreationOrchestratorDatabaseTest::testCalculateUserTools": 8, "ProjectCreationOrchestratorDatabaseTest::testGetModelForm": 8, "ProjectCreationOrchestratorDatabaseTest::testCreateMinimalProject": 1, "ProjectCreationOrchestratorDatabaseTest::testCreateProjectWithOptions": 1, "ProjectCreationOrchestratorDatabaseTest::testCreateProjectWithFiles": 1, "ProjectCreationOrchestratorIntegrationTest::testCreateProjectWithEmptyOptionsAndFiles": 8, "ProjectCreationOrchestratorIntegrationTest::testCreateProjectWithError": 8, "ProjectCreationOrchestratorIntegrationTest::testGetModelForm": 8, "ProjectCreationOrchestratorIntegrationTest::testCalculateUserTools": 8, "ProjectCreationOrchestratorIntegrationTest::testCreateProjectIntegration": 8, "ProjectCreationOrchestratorQueueTest::testQueueJobsForAutopackProject": 8, "ProjectCreationOrchestratorQueueTest::testQueueJobsForAlientechProject": 8, "ProjectCreationOrchestratorQueueTest::testCreateProjectWithoutFiles": 8, "ProjectCreationOrchestratorQueueTest::testQueueJobsWithError": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionJoin": 5, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionTruncate": 5, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFileUploadedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectClosedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFileProcessingErrorEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectCreatedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFilePackedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFileProcessedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectNoteAddedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectUpdatedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFileUnpackedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectStatusChangedEvent": 8, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendTelegram": 8, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendWithError": 8, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendUi": 8, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendEmail": 8, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendWithUnknownChannel": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsHighPriorityOverridesSupport": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testGetChannelsForRoleReturnsFilteredChannels": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsFiltersInactiveChannels": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testGetAllChannelsReturnsAllRegisteredChannels": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsWithConfigDefaults": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsWithDefaultPreferences": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsFiltersUnsupportedPriorities": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsWithUserPreferences": 8, "common\\chip\\notification\\tests\\NotificationRouterTest::testRouteNotification": 8, "common\\chip\\notification\\tests\\NotificationABTestTest::testCompare": 8, "common\\chip\\notification\\tests\\NotificationABTestTest::testCaptureNewSystemResult": 7, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testLogNotificationAttempt": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testGetSuccessRateWithFilter": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testGetSuccessRate": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testGetChannelUsage": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testGetEventTypeStats": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testLogNotificationAttemptWithNewNotification": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testLogNotificationAttemptFailure": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testLogNotificationAttemptWithRecipientWithoutId": 8, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessFileUploadedNotification": 1, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessHandlesUnknownEventTypes": 1, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessWithExistingCreatedAt": 1, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testGetFileTypeIcon": 1, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessAddsLinksForFileEvents": 1, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessPreservesOriginalData": 1, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessAddsCreatedAtIfMissing": 1, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testFormatFileSize": 1, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testGetProcessMethodName": 1, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessAddsLinksForProjectEvents": 1, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessProjectCreatedNotification": 1, "common\\chip\\notification\\tests\\NotificationFacadeTest::testNotifyProjectCreated": 7, "common\\chip\\notification\\tests\\NotificationFacadeTest::testNotifyNoteAdded": 7, "common\\chip\\notification\\tests\\NotificationFacadeTest::testNotifyFileUploaded": 7, "common\\chip\\notification\\tests\\NotificationFacadeTest::testGetters": 7, "common\\chip\\notification\\tests\\NotificationFacadeTest::testIsFeatureEnabled": 7, "common\\chip\\notification\\tests\\NotificationFacadeTest::testNotifyProjectCreatedWithLegacySystem": 7, "common\\chip\\notification\\tests\\NotificationFacadeTest::testIsChannelEnabled": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderTitle": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionLower": 5, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionDate": 5, "common\\chip\\notification\\tests\\TemplateEngineTest::testRegisterFunction": 5, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderWithNestedData": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testRender": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testGetFunctions": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderWithUnknownFunction": 5, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderWithMissingVariable": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderWithFunction": 5, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderBody": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionUpper": 5, "common\\chip\\notification\\tests\\NotificationFactoryTest::testCreateFromEvent": 7, "common\\chip\\notification\\tests\\NotificationServiceTest::testProcessEventWithNullNotificationId": 8, "common\\chip\\notification\\tests\\NotificationRouterTest::testRouteWithCustomHandler": 8, "common\\chip\\notification\\tests\\NotificationFactoryTest::testRegisterTemplate": 8, "common\\chip\\notification\\tests\\NotificationFactoryTest::testGetAllTemplates": 8, "common\\chip\\notification\\tests\\NotificationFactoryTest::testFindTemplateForEvent": 7, "common\\chip\\notification\\tests\\NotificationFactoryTest::testCreateFromEventWithoutTemplate": 7, "tests\\PHPUnit\\notification\\LegacyNotificationAdapterTest::testAdaptAndSendWithError": 8, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testGetSuccessRate": 8, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testGetChannelUsage": 8, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testGetSuccessRateWithFilter": 8, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testLogNotificationAttempt": 8, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testLogNotificationAttemptFailure": 8, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testGetEventTypeStats": 8, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testLogNotificationAttemptWithNewNotification": 8, "tests\\PHPUnit\\notification\\NotificationABTestTest::testCompare": 8, "tests\\PHPUnit\\notification\\NotificationABTestTest::testCaptureNewSystemResult": 8, "tests\\PHPUnit\\notification\\NotificationRouterTest::testRouteNotification": 8, "tests\\PHPUnit\\notification\\NotificationFacadeTest::testNotifyNoteAdded": 8, "tests\\PHPUnit\\notification\\NotificationFacadeTest::testGetters": 8, "tests\\PHPUnit\\notification\\NotificationFacadeTest::testNotifyProjectCreatedWithLegacySystem": 8, "tests\\PHPUnit\\notification\\NotificationFacadeTest::testIsFeatureEnabled": 8, "tests\\PHPUnit\\notification\\NotificationFacadeTest::testNotifyProjectCreated": 8, "tests\\PHPUnit\\notification\\NotificationFacadeTest::testIsChannelEnabled": 8, "tests\\PHPUnit\\notification\\NotificationFacadeTest::testNotifyFileUploaded": 8, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testSelectChannelsHighPriorityOverridesSupport": 8, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testSelectChannelsWithDefaultPreferences": 8, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testSelectChannelsFiltersInactiveChannels": 8, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testSelectChannelsWithUserPreferences": 8, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testSelectChannelsWithConfigDefaults": 8, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testSelectChannelsFiltersUnsupportedPriorities": 8, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessProjectCreatedNotification": 1, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessAddsLinksForFileEvents": 1, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessAddsLinksForProjectEvents": 1, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessFileUploadedNotification": 1, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessPreservesOriginalData": 1, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testGetFileTypeIcon": 1, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessWithExistingCreatedAt": 1, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testFormatFileSize": 1, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessAddsCreatedAtIfMissing": 1, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testGetProcessMethodName": 1, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessHandlesUnknownEventTypes": 1}, "times": {"SimpleTest::testTrue": 0.001, "YiiTest::testYiiApp": 0.006, "PHPUnitECUProjectServiceTest::testCreateProject": 0.095, "PHPUnitECUProjectServiceTest::testLoggerIsCalled": 0.033, "PHPUnitECUProjectServiceTest::testProjectHelperStatusConstants": 0.002, "PHPUnitECUFileServiceTest::testFileTypeConstants": 0, "PHPUnitECUFileServiceTest::testLoggerIsCalled": 0, "MockCodeceptionTest::testCreateProject": 0.004, "PHPUnit_ECUProjectServiceTest::testCreateProject": 0.008, "PHPUnit_ECUProjectServiceTest::testUpdateProjectStatus": 0, "PHPUnit_ECUProjectServiceTest::testGetProjectById": 0.001, "PHPUnit_ECUProjectServiceTest::testProjectExists": 0.001, "PHPUnit_SimpleTest::testProjectHelperConstants": 0, "PHPUnit_SimpleTest::testLoggerIsCalled": 0.001, "PHPUnit_ECUProjectServiceMockTest::testProjectExistsWithMock": 0.002, "PHPUnit_ECUProjectServiceMockTest::testUpdateProjectStatusWithMock": 0.004, "PHPUnit_ECUProjectServiceMockTest::testCreateProjectWithMock": 0, "PHPUnit_ECUProjectServiceMockTest::testGetProjectByIdWithMock": 0.036, "PHPUnit_ECUFileServiceMockTest::testSaveProjectFileWithMock": 0, "PHPUnit_ECUFileServiceMockTest::testGetProjectFilesByProjectIdWithMock": 0.001, "PHPUnit_ECUFileServiceMockTest::testGetProjectFileByIdWithMock": 0, "PHPUnit_ECUFileServiceMockTest::testUpdateProjectFileWithMock": 0, "PHPUnit_DecodingServiceMockTest::testStartDecodingWithMock": 0.001, "PHPUnit_DecodingServiceMockTest::testProcessDecodingResultWithInvalidDataMock": 0.001, "PHPUnit_DecodingServiceMockTest::testProcessDecodingResultWithMock": 0.001, "PHPUnit_DecodingServiceMockTest::testProcessDecodingResultWithErrorMock": 0, "PHPUnit_ProjectCreationServiceTest::testCreateProjectError": 0.208, "PHPUnit_ProjectCreationServiceTest::testGetModelFormForRegularUser": 0, "PHPUnit_ProjectCreationServiceTest::testGetModelFormForMsDealer": 0.022, "PHPUnit_ProjectCreationServiceTest::testGetModelFormForDealer": 0.005, "PHPUnit_ProjectCreationServiceTest::testCreateProjectNull": 0, "PHPUnit_ProjectCreationServiceTest::testCreateProjectSuccess": 0.06, "PHPUnit_ProjectOptionsServiceTest::testProcessOptionsEmpty": 0.137, "PHPUnit_ProjectOptionsServiceTest::testProcessOptionsWithAdditions": 0, "PHPUnit_ProjectOptionsServiceTest::testProcessOptionsWithRequest": 0.025, "PHPUnit_ProjectFileProcessingServiceTest::testProcessFilesWithMultipleFiles": 0.333, "PHPUnit_ProjectFileProcessingServiceTest::testProcessFilesEmpty": 0.038, "PHPUnit_ProjectFileProcessingServiceTest::testProcessFilesWithSlaveFileForAlientech": 0.044, "PHPUnit_ProjectFileProcessingServiceTest::testProcessFilesWithMasterFileForAutopack": 0.021, "ProjectCreationOrchestratorDatabaseTest::testCreateMinimalProject": 0, "ProjectCreationOrchestratorDatabaseTest::testCreateProjectWithOptions": 0, "ProjectCreationOrchestratorDatabaseTest::testCreateProjectWithFiles": 0, "ProjectCreationOrchestratorDatabaseTest::testCalculateUserTools": 0.275, "ProjectCreationOrchestratorDatabaseTest::testGetModelForm": 0.005, "ProjectCreationOrchestratorIntegrationTest::testCalculateUserTools": 0.028, "ProjectCreationOrchestratorIntegrationTest::testCreateProjectWithEmptyOptionsAndFiles": 0.214, "ProjectCreationOrchestratorIntegrationTest::testCreateProjectIntegration": 0.012, "ProjectCreationOrchestratorIntegrationTest::testGetModelForm": 0.011, "ProjectCreationOrchestratorIntegrationTest::testCreateProjectWithError": 0.145, "common\\chip\\notification\\tests\\EventBusTest::testSubscribeAndPublish": 0.004, "common\\chip\\notification\\tests\\EventBusTest::testWildcardListener": 0, "common\\chip\\notification\\tests\\EventBusTest::testUnsubscribe": 0, "common\\chip\\notification\\tests\\EventBusTest::testClearEventLog": 0.033, "common\\chip\\notification\\tests\\EventBusTest::testEventLog": 0.205, "common\\chip\\notification\\tests\\EventBusTest::testMultipleListeners": 0, "common\\chip\\notification\\tests\\NotificationFactoryTest::testFindTemplateForEvent": 0.129, "common\\chip\\notification\\tests\\NotificationFactoryTest::testCreateFromEvent": 0.07, "common\\chip\\notification\\tests\\NotificationFactoryTest::testCreateFromEventWithoutTemplate": 0.021, "common\\chip\\notification\\tests\\NotificationFactoryTest::testGetAllTemplates": 0.206, "common\\chip\\notification\\tests\\NotificationFactoryTest::testGetTemplateEngine": 0, "common\\chip\\notification\\tests\\NotificationFactoryTest::testRegisterTemplate": 0.013, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderWithUnknownFunction": 0, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderTitle": 0, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderWithMissingVariable": 0.08, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionJoin": 0, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderWithNestedData": 0, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionLower": 0, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderBody": 0.263, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionDate": 0, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionUpper": 0, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionTruncate": 0, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderWithFunction": 0, "common\\chip\\notification\\tests\\TemplateEngineTest::testGetFunctions": 0.008, "common\\chip\\notification\\tests\\TemplateEngineTest::testRender": 0, "common\\chip\\notification\\tests\\TemplateEngineTest::testRegisterFunction": 0, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFileUploadedEvent": 0.005, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectClosedEvent": 0.005, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFileProcessingErrorEvent": 0.004, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectCreatedEvent": 0.005, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testGetEventBus": 0.005, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFilePackedEvent": 0.203, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFileProcessedEvent": 0.005, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testInvalidActionReturnsNull": 0.011, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectNoteAddedEvent": 0.005, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectUpdatedEvent": 0.005, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFileUnpackedEvent": 0.005, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectStatusChangedEvent": 0.005, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testGetLegacyService": 0, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendTelegram": 0.011, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendWithError": 0.076, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendUi": 0.004, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendEmail": 0, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendWithUnknownChannel": 0, "common\\chip\\notification\\tests\\NotificationServiceTest::testProcessEventWithNullNotificationId": 0.004, "common\\chip\\notification\\tests\\NotificationServiceTest::testProcessEvent": 0.134, "common\\chip\\notification\\tests\\NotificationServiceTest::testLogSizeLimit": 0.015, "common\\chip\\notification\\tests\\NotificationServiceTest::testClearProcessedLog": 0.022, "common\\chip\\notification\\tests\\NotificationServiceTest::testGetRouter": 0, "common\\chip\\notification\\tests\\NotificationServiceTest::testGetFactory": 0.094, "common\\chip\\notification\\tests\\NotificationRouterTest::testRouteNotification": 0.029, "common\\chip\\notification\\tests\\NotificationRouterTest::testRouteWithCustomHandler": 0, "common\\chip\\notification\\tests\\NotificationRouterTest::testGettersReturnCorrectInstances": 0, "common\\chip\\notification\\tests\\NotificationRouterTest::testGetHandlerReturnsCorrectHandler": 0, "common\\chip\\notification\\tests\\NotificationABTestTest::testExtractDataFromEvent": 0, "common\\chip\\notification\\tests\\NotificationABTestTest::testCompare": 0.015, "common\\chip\\notification\\tests\\NotificationABTestTest::testCaptureNewSystemResult": 0.022, "common\\chip\\notification\\tests\\NotificationABTestTest::testMapEventTypeToAction": 0, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testLogNotificationAttempt": 0.017, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testGetSuccessRateWithFilter": 0.017, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testGetSuccessRate": 0.017, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testGetChannelUsage": 0.017, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testGetEventTypeStats": 0.017, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testLogNotificationAttemptWithNewNotification": 0.017, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testLogNotificationAttemptFailure": 0.017, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testLogNotificationAttemptWithRecipientWithoutId": 0.008, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessFileUploadedNotification": 0, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessHandlesUnknownEventTypes": 0, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessWithExistingCreatedAt": 0, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testGetFileTypeIcon": 0, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessAddsLinksForFileEvents": 0, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessPreservesOriginalData": 0, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessAddsCreatedAtIfMissing": 0, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testFormatFileSize": 0, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testGetProcessMethodName": 0, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessAddsLinksForProjectEvents": 0, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessProjectCreatedNotification": 0, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testGetChannelsForRoleReturnsFilteredChannels": 0, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsWithDefaultPreferences": 0.017, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsHighPriorityOverridesSupport": 0.018, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsFiltersInactiveChannels": 0.017, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testGetAllChannelsReturnsAllRegisteredChannels": 0.006, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsFiltersUnsupportedPriorities": 0.017, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsWithConfigDefaults": 0.017, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsWithUserPreferences": 0.017, "common\\chip\\notification\\tests\\NotificationFacadeTest::testIsFeatureEnabled": 0, "common\\chip\\notification\\tests\\NotificationFacadeTest::testIsChannelEnabled": 0, "common\\chip\\notification\\tests\\NotificationFacadeTest::testNotifyNoteAdded": 0, "common\\chip\\notification\\tests\\NotificationFacadeTest::testGetters": 0, "common\\chip\\notification\\tests\\NotificationFacadeTest::testNotifyFileUploaded": 0, "common\\chip\\notification\\tests\\NotificationFacadeTest::testNotifyProjectCreated": 0, "common\\chip\\notification\\tests\\NotificationFacadeTest::testNotifyProjectCreatedWithLegacySystem": 0, "tests\\PHPUnit\\notification\\EventBusTest::testEventLog": 0, "tests\\PHPUnit\\notification\\EventBusTest::testUnsubscribe": 0.009, "tests\\PHPUnit\\notification\\EventBusTest::testClearEventLog": 0.024, "tests\\PHPUnit\\notification\\EventBusTest::testMultipleListeners": 0, "tests\\PHPUnit\\notification\\EventBusTest::testWildcardListener": 0, "tests\\PHPUnit\\notification\\EventBusTest::testSubscribeAndPublish": 0, "tests\\PHPUnit\\notification\\LegacyEventEmitterTest::testEmitFileProcessedEvent": 0.005, "tests\\PHPUnit\\notification\\LegacyEventEmitterTest::testEmitProjectClosedEvent": 0.005, "tests\\PHPUnit\\notification\\LegacyEventEmitterTest::testEmitFilePackedEvent": 0.005, "tests\\PHPUnit\\notification\\LegacyEventEmitterTest::testEmitProjectStatusChangedEvent": 0.005, "tests\\PHPUnit\\notification\\LegacyEventEmitterTest::testEmitFileUnpackedEvent": 0.005, "tests\\PHPUnit\\notification\\LegacyEventEmitterTest::testEmitFileProcessingErrorEvent": 0.005, "tests\\PHPUnit\\notification\\LegacyEventEmitterTest::testGetEventBus": 0, "tests\\PHPUnit\\notification\\LegacyEventEmitterTest::testInvalidActionReturnsNull": 0, "tests\\PHPUnit\\notification\\LegacyEventEmitterTest::testEmitFileUploadedEvent": 0.005, "tests\\PHPUnit\\notification\\LegacyEventEmitterTest::testEmitProjectUpdatedEvent": 0.005, "tests\\PHPUnit\\notification\\LegacyEventEmitterTest::testEmitProjectCreatedEvent": 0, "tests\\PHPUnit\\notification\\LegacyEventEmitterTest::testEmitProjectNoteAddedEvent": 0.005, "tests\\PHPUnit\\notification\\LegacyNotificationAdapterTest::testAdaptAndSendUi": 0.32, "tests\\PHPUnit\\notification\\LegacyNotificationAdapterTest::testAdaptAndSendWithError": 0.124, "tests\\PHPUnit\\notification\\LegacyNotificationAdapterTest::testAdaptAndSendTelegram": 0, "tests\\PHPUnit\\notification\\LegacyNotificationAdapterTest::testGetLegacyService": 0.005, "tests\\PHPUnit\\notification\\LegacyNotificationAdapterTest::testAdaptAndSendWithUnknownChannel": 0.01, "tests\\PHPUnit\\notification\\LegacyNotificationAdapterTest::testAdaptAndSendEmail": 0, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testGetSuccessRate": 0.039, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testGetChannelUsage": 0.018, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testGetSuccessRateWithFilter": 0.019, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testLogNotificationAttempt": 0.018, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testLogNotificationAttemptFailure": 0.018, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testGetEventTypeStats": 0.019, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testLogNotificationAttemptWithRecipientWithoutId": 0.019, "tests\\PHPUnit\\notification\\NotificationMonitoringServiceTest::testLogNotificationAttemptWithNewNotification": 0.017, "tests\\PHPUnit\\notification\\NotificationABTestTest::testCompare": 0.025, "tests\\PHPUnit\\notification\\NotificationABTestTest::testMapEventTypeToAction": 0.008, "tests\\PHPUnit\\notification\\NotificationABTestTest::testCaptureNewSystemResult": 0.013, "tests\\PHPUnit\\notification\\NotificationABTestTest::testExtractDataFromEvent": 0, "tests\\PHPUnit\\notification\\NotificationFactoryTest::testGetAllTemplates": 0.012, "tests\\PHPUnit\\notification\\NotificationFactoryTest::testRegisterTemplate": 0, "tests\\PHPUnit\\notification\\NotificationFactoryTest::testGetTemplateEngine": 0, "tests\\PHPUnit\\notification\\NotificationFactoryTest::testCreateFromEvent": 0.008, "tests\\PHPUnit\\notification\\NotificationFactoryTest::testFindTemplateForEvent": 0.005, "tests\\PHPUnit\\notification\\NotificationFactoryTest::testCreateFromEventWithoutTemplate": 0.007, "tests\\PHPUnit\\notification\\NotificationRouterTest::testRouteNotification": 0.017, "tests\\PHPUnit\\notification\\NotificationRouterTest::testGettersReturnCorrectInstances": 0, "tests\\PHPUnit\\notification\\NotificationRouterTest::testRouteWithCustomHandler": 0, "tests\\PHPUnit\\notification\\NotificationRouterTest::testGetHandlerReturnsCorrectHandler": 0, "tests\\PHPUnit\\notification\\NotificationServiceTest::testGetFactory": 0, "tests\\PHPUnit\\notification\\NotificationServiceTest::testGetRouter": 0, "tests\\PHPUnit\\notification\\NotificationServiceTest::testLogSizeLimit": 0.017, "tests\\PHPUnit\\notification\\NotificationServiceTest::testProcessEventWithNullNotificationId": 0, "tests\\PHPUnit\\notification\\NotificationServiceTest::testProcessEvent": 0, "tests\\PHPUnit\\notification\\NotificationServiceTest::testClearProcessedLog": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testDefaultFunctionUpper": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testRenderWithMissingVariable": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testRenderWithFunction": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testDefaultFunctionLower": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testRenderWithNestedData": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testRender": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testDefaultFunctionTruncate": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testRegisterFunction": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testGetFunctions": 0.008, "tests\\PHPUnit\\notification\\TemplateEngineTest::testDefaultFunctionDate": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testRenderTitle": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testDefaultFunctionJoin": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testRenderBody": 0, "tests\\PHPUnit\\notification\\TemplateEngineTest::testRenderWithUnknownFunction": 0, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testSelectChannelsHighPriorityOverridesSupport": 0.02, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testSelectChannelsWithDefaultPreferences": 0.019, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testGetAllChannelsReturnsAllRegisteredChannels": 0.007, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testSelectChannelsFiltersInactiveChannels": 0.018, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testSelectChannelsWithUserPreferences": 0.017, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testSelectChannelsWithConfigDefaults": 0.017, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testSelectChannelsFiltersUnsupportedPriorities": 0.017, "tests\\PHPUnit\\notification\\DefaultChannelSelectorTest::testGetChannelsForRoleReturnsFilteredChannels": 0, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessProjectCreatedNotification": 0, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessAddsLinksForFileEvents": 0, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessAddsLinksForProjectEvents": 0, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessFileUploadedNotification": 0, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessPreservesOriginalData": 0, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testGetFileTypeIcon": 0, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessWithExistingCreatedAt": 0, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testFormatFileSize": 0, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessAddsCreatedAtIfMissing": 0, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testGetProcessMethodName": 0, "tests\\PHPUnit\\notification\\NotificationPreProcessorTest::testProcessHandlesUnknownEventTypes": 0}}