<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "api1C_log".
 *
 * @property int $id
 * @property string $url
 * @property string $req_data Запрос на 1С сервер
 * @property string $resp_data Данные с сервера 1С
 * @property string $created_at
 * @property string $updated_at
 * @property string $deleted_at
 * @property int $updated_by
 * @property int $deleted_by
 * @property int $isDeleted
 */
class Api1CLog extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'api1C_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['req_data', 'resp_data'], 'string'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['updated_by', 'deleted_by', 'isDeleted'], 'integer'],
            [['url'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'url' => Yii::t('app', 'Url'),
            'req_data' => Yii::t('app', 'Req Data'),
            'resp_data' => Yii::t('app', 'Resp Data'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'deleted_at' => Yii::t('app', 'Deleted At'),
            'updated_by' => Yii::t('app', 'Updated By'),
            'deleted_by' => Yii::t('app', 'Deleted By'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
        ];
    }

    /**
     * {@inheritdoc}
     * @return Api1CLogQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new Api1CLogQuery(get_called_class());
    }
}
