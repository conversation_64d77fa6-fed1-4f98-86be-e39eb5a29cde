<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[AutopackSetting]].
 *
 * @see AutopackSetting
 */
class AutopackSettingsQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return AutopackSetting[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return AutopackSetting|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
