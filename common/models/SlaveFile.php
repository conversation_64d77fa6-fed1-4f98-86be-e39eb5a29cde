<?php

namespace common\models;

use common\jobs\SlaveFilesJob;
use Yii;

/**
 * This is the model class for table "slave_files".
 *
 * @property int $id
 * @property int|null $file_id
 * @property int|null $project_id
 * @property string|null $slot_guid
 * @property string|null $file_guid
 * @property int $status
 * @property int $finished
 * @property int $errors_count
 * @property int $blocked
 * @property string|null $created_at
 * @property string|null $updated_at
 */
class SlaveFile extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'slave_files';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['file_id', 'status', 'finished', 'project_id', 'errors_count', 'blocked'], 'integer'],
            [['status'], 'required'],
            [['created_at', 'updated_at', 'finished', 'slot_guid', 'file_guid'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'file_id' => Yii::t('app', 'File ID'),
            'project_id' => Yii::t('app', 'Project ID'),
            'status' => Yii::t('app', 'Status'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
        ];
    }
    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes); // TODO: Change the autogenerated stub
//        Yii::$app->queue->push(new SlaveFilesJob());
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\SlaveFilesQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\SlaveFilesQuery(get_called_class());
    }

    /**
     * @return int|null
     */
    public function getFileId(): ?int
    {
        return $this->file_id;
    }

    /**
     * @param int|null $file_id
     */
    public function setFileId(?int $file_id): void
    {
        $this->file_id = $file_id;
    }

    /**
     * @return int|null
     */
    public function getProjectId(): ?int
    {
        return $this->project_id;
    }

    /**
     * @param int|null $project_id
     */
    public function setProjectId(?int $project_id): void
    {
        $this->project_id = $project_id;
    }

    /**
     * @return string|null
     */
    public function getSlotGuid(): ?string
    {
        return $this->slot_guid;
    }

    /**
     * @param string|null $slot_guid
     */
    public function setSlotGuid(?string $slot_guid): void
    {
        $this->slot_guid = $slot_guid;
    }

    /**
     * @return int
     */
    public function getStatus(): int
    {
        return $this->status;
    }

    /**
     * @param int $status
     */
    public function setStatus(int $status): void
    {
        $this->status = $status;
    }

    /**
     * @return int
     */
    public function getFinished(): int
    {
        return $this->finished;
    }

    /**
     * @param int $finished
     */
    public function setFinished(int $finished): void
    {
        $this->finished = $finished;
    }


}
