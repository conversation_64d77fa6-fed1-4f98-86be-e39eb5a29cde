<?php

namespace common\models\notification;

use common\models\User;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Expression;

/**
 * Модель для таблицы "notification_preferences"
 *
 * @property int $id
 * @property int $user_id ID пользователя
 * @property string $notification_type Тип уведомления
 * @property int $channel_id Канал доставки
 * @property bool $is_enabled Включены ли уведомления этого типа
 * @property string $created_at Дата и время создания
 * @property string $updated_at Дата и время обновления
 *
 * @property User $user Пользователь
 * @property NotificationChannel $channel Канал
 */
class NotificationPreference extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%notification_preferences}}';
    }
    
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => new Expression('NOW()'),
            ],
        ];
    }
    
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'notification_type', 'channel_id'], 'required'],
            [['user_id', 'channel_id'], 'integer'],
            [['is_enabled'], 'boolean'],
            [['created_at', 'updated_at'], 'safe'],
            [['notification_type'], 'string', 'max' => 50],
            [['user_id', 'notification_type', 'channel_id'], 'unique', 'targetAttribute' => ['user_id', 'notification_type', 'channel_id']],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
            [['channel_id'], 'exist', 'skipOnError' => true, 'targetClass' => NotificationChannel::class, 'targetAttribute' => ['channel_id' => 'id']],
        ];
    }
    
    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'ID пользователя',
            'notification_type' => 'Тип уведомления',
            'channel_id' => 'ID канала',
            'is_enabled' => 'Включено',
            'created_at' => 'Дата создания',
            'updated_at' => 'Дата обновления',
        ];
    }
    
    /**
     * Получить пользователя
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }
    
    /**
     * Получить канал
     *
     * @return \yii\db\ActiveQuery
     */
    public function getChannel()
    {
        return $this->hasOne(NotificationChannel::class, ['id' => 'channel_id']);
    }
    
    /**
     * Проверить, включены ли уведомления определенного типа для пользователя через определенный канал
     *
     * @param int $userId ID пользователя
     * @param string $notificationType Тип уведомления
     * @param string $channelCode Код канала
     * @return bool
     */
    public static function isEnabled($userId, $notificationType, $channelCode)
    {
        $channelId = NotificationChannel::getIdByCode($channelCode);
        if (!$channelId) {
            return false;
        }
        
        $preference = static::findOne([
            'user_id' => $userId,
            'notification_type' => $notificationType,
            'channel_id' => $channelId,
        ]);
        
        if (!$preference) {
            // Если настройка не найдена, возвращаем значение по умолчанию (true)
            return true;
        }
        
        return $preference->is_enabled;
    }
    
    /**
     * Установить настройку уведомлений для пользователя
     *
     * @param int $userId ID пользователя
     * @param string $notificationType Тип уведомления
     * @param string $channelCode Код канала
     * @param bool $isEnabled Включено ли уведомление
     * @return bool
     */
    public static function setPreference($userId, $notificationType, $channelCode, $isEnabled)
    {
        $channelId = NotificationChannel::getIdByCode($channelCode);
        if (!$channelId) {
            return false;
        }
        
        $preference = static::findOne([
            'user_id' => $userId,
            'notification_type' => $notificationType,
            'channel_id' => $channelId,
        ]);
        
        if (!$preference) {
            $preference = new static();
            $preference->user_id = $userId;
            $preference->notification_type = $notificationType;
            $preference->channel_id = $channelId;
        }
        
        $preference->is_enabled = $isEnabled;
        
        return $preference->save();
    }
}
