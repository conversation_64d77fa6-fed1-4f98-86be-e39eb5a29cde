<?php

namespace common\models\search;

use backend\widgets\GridView;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\ChipEcuAdditions;

/**
 * ChipEcuAdditionsSearch represents the model behind the search form about `common\models\ChipEcuAdditions`.
 */
class ChipEcuAdditionsSearch extends ChipEcuAdditions
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'ecu_id'], 'integer'],
            [['created_at', 'updated_at', 'addition_id', 'deleted_at', 'brand_id', 'model_id', 'generation_id', 'engine_id'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ChipEcuAdditions::find();
        $query->groupBy('ecu_id');

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'params' => GridView::getMergedFilterStateParams(),
            ],
            'sort' => [
                'params' => GridView::getMergedFilterStateParams(),
            ],
        ]);
// Filter model
        $this->load(GridView::getMergedFilterStateParams());
        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'id' => $this->id,
            'brand_id' => $this->brand_id,
            'model_id' => $this->model_id,
            'generation_id' => $this->generation_id,
            'engine_id' => $this->engine_id,
            'ecu_id' => $this->ecu_id,
//            'addition_id' => $this->addition_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->deleted_at,
        ]);

        if (strlen($this->addition_id) > 0) {
//            $query->joinWith(['additions' => function ($q) {
//                $q->where('pip_problems.pip_title LIKE "%' . $this->pip_list . '%"');
//                $q->orWhere('pip_problems.pip_number LIKE "%' . $this->pip_list . '%"');
//            }]);

        }

        return $dataProvider;
    }
}
