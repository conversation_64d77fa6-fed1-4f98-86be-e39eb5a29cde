<?php

namespace common\models;

use common\models\query\ChipStagesEcuQuery;
use Yii;
use yii2tech\ar\softdelete\SoftDeleteBehavior;

/**
 * This is the model class for table "chip_stages_ecu".
 *
 * @property int $id
 * @property int $stage_id
 * @property int $ecu_id
 * @property int $chip_ecu_id
 * @property int $inc_hp
 * @property int $inc_tork
 * @property int $comment
 * @property int $price
 * @property int $dinostend_file
 * @property int $inc_params
 * @property string $created_at
 * @property string $deleted_at
 * @property int $isDeleted
 * @property int $deleted_by

 * @property ChipStages $stage
 * @property ChipEcu $ecu
 */
class ChipStagesEcu extends BaseChipModel
{
    public function behaviors()
    {
        return [
            'softDeleteBehavior' => [
                'class' => SoftDeleteBehavior::className(),
                'softDeleteAttributeValues' => [
                    'isDeleted' => true
                ],
                'replaceRegularDelete' => true // mutate native `delete()` method
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'chip_stages_ecu';
    }

//    public function fields()
//    {
//        return [
//            'stage_id', 'inc_hp', 'inc_tork', 'comment',
//        ];
//    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['stage_id', 'ecu_id'], 'required'],
            [['stage_id', 'ecu_id', 'chip_ecu_id'], 'integer'],
            [['created_at', 'deleted_at', 'inc_hp', 'inc_tork', 'inc_params', 'comment', 'price', 'chip_ecu_id', 'isDeleted', 'deleted_by', 'dinostend_file'], 'safe'],
            [['stage_id'], 'exist', 'skipOnError' => true, 'targetClass' => ChipStages::className(), 'targetAttribute' => ['stage_id' => 'id']],
            [['ecu_id'], 'exist', 'skipOnError' => true, 'targetClass' => ChipEcu::className(), 'targetAttribute' => ['ecu_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('backend', 'ID'),
            'stage_id' => Yii::t('backend', 'Stage ID'),
            'ecu_id' => Yii::t('backend', 'Ecu ID'),
            'inc_hp' => Yii::t('backend', 'Inc HP'),
            'inc_tork' => Yii::t('backend', 'Inc Tork'),
            'inc_params' => Yii::t('backend', 'Inc Params'),
            'dinostend_file' => Yii::t('backend', 'Dinostend file'),
            'comment' => Yii::t('backend', 'Comment'),
            'price' => Yii::t('backend', 'Price'),
            'created_at' => Yii::t('backend', 'Created At'),
            'deleted_at' => Yii::t('backend', 'Deleted At'),
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getStage()
    {
        return $this->hasOne(ChipStages::className(), ['id' => 'stage_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getEcu()
    {
        return $this->hasOne(ChipEcu::className(), ['id' => 'ecu_id']);
    }

    /**
     * {@inheritdoc}
     * @return ChipStagesEcuQuery the active query used by this AR class.
     */
    public static function find()
    {
        return parent::find(new ChipStagesEcuQuery(get_called_class()));
    }
}
