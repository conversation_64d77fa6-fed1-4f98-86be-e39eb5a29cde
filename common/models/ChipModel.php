<?php

namespace common\models;

use common\models\query\ChipModelQuery;
use Yii;

/**
 * This is the model class for table "chip_model".
 *
 * @property int $id
 * @property int $brand_id
 * @property string $title
 * @property string $slug
 * @property string $created_at
 * @property string $updated_at
 * @property string $deleted_at
 *
 * @property ChipEcu[] $chipEcus
 * @property ChipEngine[] $chipEngines
 * @property ChipGeneration[] $chipGenerations
 * @property ChipBrand $brand
 */
class ChipModel extends BaseChipModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'chip_model';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['brand_id', 'title'], 'required'],
            [['brand_id'], 'integer'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['title', 'slug'], 'string', 'max' => 255],
            [['brand_id'], 'exist', 'skipOnError' => true, 'targetClass' => ChipBrand::className(), 'targetAttribute' => ['brand_id' => 'id']],
        ];
    }
    public function fields()
    {
        return [
            'id', 'brand_id', 'title'
        ];
    }
    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('backend', 'ID'),
            'brand_id' => Yii::t('backend', 'Brand ID'),
            'title' => Yii::t('backend', 'Title'),
            'slug' => Yii::t('backend', 'Slug'),
            'created_at' => Yii::t('backend', 'Created At'),
            'updated_at' => Yii::t('backend', 'Updated At'),
            'deleted_at' => Yii::t('backend', 'Deleted At'),
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getChipEcus()
    {
        return $this->hasMany(ChipEcu::className(), ['model_id' => 'id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getChipEngines()
    {
        return $this->hasMany(ChipEngine::className(), ['model_id' => 'id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getChipGenerations()
    {
        return $this->hasMany(ChipGeneration::className(), ['model_id' => 'id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getChipGenerationsWithEngines()
    {
        return $this->hasMany(ChipGeneration::className(), ['model_id' => 'id'])->with('chipEnginesWithEcus');
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getBrand()
    {
        return $this->hasOne(ChipBrand::className(), ['id' => 'brand_id']);
    }

    /**
     * {@inheritdoc}
     * @return ChipModelQuery the active query used by this AR class.
     */
    public static function find()
    {
        return parent::find(new ChipModelQuery(get_called_class()));
    }
    /**
     *
     */
    public function afterSoftDelete()
    {
        if ($this->chipGenerations) {
            foreach($this->chipGenerations as $k => $model)
            {
                $model->delete();
            }
        }
    }

    /**
     * {@inheritdoc}
     * @return boolean.
     */
    public function beforeRestore()
    {
//        echo '<br>'.self::class.'beforeRestore';
//        if () {
            $this->brand->restore();
            return true;
//        }
//        return false;
    }


}
