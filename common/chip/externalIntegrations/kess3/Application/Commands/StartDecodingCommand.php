<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Application\Commands;

/**
 * Команда для начала декодинга файла
 */
final class StartDecodingCommand
{
    public function __construct(
        private readonly string $filePath,
        private readonly int $projectId,
        private readonly int $fileId,
        private readonly ?int $userId = null,
        private readonly array $additionalData = []
    ) {
        if (empty($filePath)) {
            throw new \InvalidArgumentException('File path cannot be empty');
        }
        
        if ($projectId <= 0) {
            throw new \InvalidArgumentException('Project ID must be positive');
        }
        
        if ($fileId <= 0) {
            throw new \InvalidArgumentException('File ID must be positive');
        }
    }
    
    public function getFilePath(): string
    {
        return $this->filePath;
    }
    
    public function getProjectId(): int
    {
        return $this->projectId;
    }
    
    public function getFileId(): int
    {
        return $this->fileId;
    }
    
    public function getUserId(): ?int
    {
        return $this->userId;
    }
    
    public function getAdditionalData(): array
    {
        return $this->additionalData;
    }
}
