<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Domain\Events;

use common\chip\event\core\BaseEvent;

/**
 * Событие завершения декодинга файла
 */
class DecodingCompletedEvent extends BaseEvent
{
    public const TYPE = 'kess3.decoding.completed';
    
    public function __construct(
        private readonly string $operationId,
        private readonly int $projectId,
        private readonly int $fileId,
        private readonly bool $successful,
        private readonly ?string $error = null,
        private readonly ?array $result = null,
        array $contextData = []
    ) {
        parent::__construct($operationId, array_merge($contextData, [
            'operationId' => $operationId,
            'projectId' => $projectId,
            'fileId' => $fileId,
            'successful' => $successful,
            'error' => $error,
            'result' => $result
        ]));
    }
    
    public function getType(): string
    {
        return self::TYPE;
    }
    
    public function getDescription(): string
    {
        $status = $this->successful ? 'успешно завершено' : 'завершено с ошибкой';
        return "Декодирование файла {$status} (проект {$this->projectId})";
    }
    
    public function getOperationId(): string
    {
        return $this->operationId;
    }
    
    public function getProjectId(): int
    {
        return $this->projectId;
    }
    
    public function getFileId(): int
    {
        return $this->fileId;
    }
    
    public function isSuccessful(): bool
    {
        return $this->successful;
    }
    
    public function getError(): ?string
    {
        return $this->error;
    }
    
    public function getResult(): ?array
    {
        return $this->result;
    }
}
