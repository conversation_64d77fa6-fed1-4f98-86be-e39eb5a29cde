<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\SlotManagement;

use common\chip\externalIntegrations\kess3\Infrastructure\Http\HttpClientInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Configuration\ConfigProviderInterface;
use Psr\Log\LoggerInterface;
use yii\caching\CacheInterface;

/**
 * Фабрика для создания SlotManager
 */
final readonly class SlotManagerFactory
{
    public function __construct(
        private CacheInterface $cache,
        private LoggerInterface $logger
    ) {}
    
    /**
     * Создает настроенный SlotManager
     */
    public function createSlotManager(
        HttpClientInterface $httpClient,
        ConfigProviderInterface $config
    ): SlotManagerInterface {
        $slotSettings = $config->getSlotSettings();
        
        return new SlotManager(
            httpClient: $httpClient,
            cache: $this->cache,
            logger: $this->logger,
            cacheDuration: $slotSettings['cache_duration'] ?? 60,
            maxSlots: $slotSettings['max_slots'] ?? 3
        );
    }
}
