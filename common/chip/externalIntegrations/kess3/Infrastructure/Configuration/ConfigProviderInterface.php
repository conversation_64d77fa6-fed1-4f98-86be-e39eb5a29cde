<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Configuration;

use common\chip\externalIntegrations\kess3\Infrastructure\Dto\AuthCredentials;

/**
 * Интерфейс для провайдера конфигурации Alientech API
 */
interface ConfigProviderInterface
{
    /**
     * Возвращает код клиента
     * 
     * @return string
     * 
     * @throws \common\chip\externalIntegrations\kess3\Domain\Exception\ConfigurationException При отсутствии конфигурации
     */
    public function getCustomerCode(): string;
    
    /**
     * Возвращает callback URL для типа операции
     * 
     * @param string $operationType Тип операции (decode, encode)
     * @return string
     * 
     * @throws \common\chip\externalIntegrations\kess3\Domain\Exception\ConfigurationException При неизвестном типе
     */
    public function getCallbackUrl(string $operationType): string;
    
    /**
     * Возвращает URL endpoint для API метода
     * 
     * @param string $endpoint Название endpoint'а
     * @return string
     * 
     * @throws \common\chip\externalIntegrations\kess3\Domain\Exception\ConfigurationException При неизвестном endpoint
     */
    public function getApiEndpoint(string $endpoint): string;
    
    /**
     * Возвращает базовый URL API
     * 
     * @return string
     */
    public function getApiBaseUrl(): string;
    
    /**
     * Возвращает учетные данные для аутентификации
     * 
     * @return AuthCredentials
     * 
     * @throws \common\chip\externalIntegrations\kess3\Domain\Exception\ConfigurationException При отсутствии учетных данных
     */
    public function getAuthCredentials(): AuthCredentials;
    
    /**
     * Возвращает настройки таймаутов
     * 
     * @return array<string, int> Массив таймаутов по типам операций
     */
    public function getTimeouts(): array;
    
    /**
     * Возвращает настройки повторных попыток
     * 
     * @return array{max_attempts: int, delay: int, backoff_multiplier: float}
     */
    public function getRetrySettings(): array;
}
