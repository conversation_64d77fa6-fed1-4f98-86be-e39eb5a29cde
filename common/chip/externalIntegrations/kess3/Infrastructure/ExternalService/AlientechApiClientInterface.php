<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\ExternalService;

/**
 * Интерфейс для клиента Alientech API
 */
interface AlientechApiClientInterface
{
    /**
     * Запустить процесс декодирования
     * 
     * @param string $filePath Путь к файлу для декодирования
     * @param string $callbackUrl URL для callback
     * @param array $userInfo Пользовательская информация
     * @return array Ответ от API с guid и slotGUID
     * 
     * @throws \RuntimeException При ошибке API
     */
    public function startDecoding(string $filePath, string $callbackUrl, array $userInfo = []): array;

    /**
     * Получить статус операции декодирования
     * 
     * @param string $operationGuid GUID операции
     * @return array Статус операции
     * 
     * @throws \RuntimeException При ошибке API
     */
    public function getOperationStatus(string $operationGuid): array;

    /**
     * Скачать декодированный файл
     * 
     * @param string $fileUrl URL файла
     * @return array Данные файла
     * 
     * @throws \RuntimeException При ошибке API
     */
    public function downloadFile(string $fileUrl): array;

    /**
     * Проверить доступность API
     * 
     * @return bool
     */
    public function isAvailable(): bool;
}
