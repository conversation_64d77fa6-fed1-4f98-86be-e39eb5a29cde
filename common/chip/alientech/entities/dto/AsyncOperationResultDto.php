<?php

namespace common\chip\alientech\entities\dto;

class AsyncOperationResultDto extends AlientechDto
{
    const TYPE_OBD = 'OBD';
    const TYPE_BOOT_BENCH = 'BootBench';
    private string $kess3FileSlotGUID;    //“706a4fd1-f7bc-4845-9a60-7909f897e050”
    private string $name;  //“ecu”
    private string $readFileURL; //“<server-base-url>/api/files/kess3/slots/706a4fd1-f7bc-4845-9a60-7909f897e050/files/Read”

    private string $kess3Mode;  //OBD or BootBench
    private string $idFileURL;  //“<server-base-url>/api/files/kess3/slots/706a4fd1-f7bc-4845-9a60-7909f897e050/files/ID”
    private string $obdDecodedFileURL;  //“<server-base-url>/api/files/kess3/slots/706a4fd1-f7bc-4845-9a60-7909f897e050/files/OBDDecoded”
    private array $bootBenchComponents;
//[
//{
//"Type":"EEPROM",
//"DecodedFileURL":"<server-base-
//url>/api/files/kess3/slots/706a4fd1-
//f7bc-4845-9a60-
//7909f897e050/files/DecodedEEPROM"
//},
//{
//    "Type":"Micro",
//      "DecodedFileURL":"<server-base-
//url>/api/files/kess3/slots/706a4fd1-
//f7bc-4845-9a60-7909f897e050/files
///DecodedMicro"
//    }
//]

    private bool $wasOriginalFileFoundThroughVirtualReading;
    private bool $isOriginalFileNeeded;
    private bool $willChecksumBeCorrected;
    private bool $isChecksumCorrectionNeeded;
    private bool $isCVNCorrectionPossible;

    private $information;//  object  //An object representing additional information that is related to the operation. Its specifications depend on the type of operation that was performed.

    private string $jsonData;

    /**
     * AsyncOperationDto constructor.
     * @param $data
     */
    public function __construct($data = null)
    {
        if (isset($data->Name)) {
            $data = $this->convertDataFromCallback($data);
        }

        $this->jsonData = json_encode($data);
        $this->kess3FileSlotGUID = $data->kess3FileSlotGUID ?? '';
        $this->name = $data->name ?? '';
        $this->readFileURL = $data->readFileURL ?? '';
        $this->kess3Mode = $data->kess3Mode ?? '';
        $this->idFileURL = $data->idFileURL ?? '';
        $this->obdDecodedFileURL = $data->obdDecodedFileURL ?? '';
        $this->bootBenchComponents = $data->bootBenchComponents ?? [];
        $this->wasOriginalFileFoundThroughVirtualReading = $data->wasOriginalFileFoundThroughVirtualReading ?? false;
        $this->isOriginalFileNeeded = $data->isOriginalFileNeeded ?? false;
        $this->willChecksumBeCorrected = $data->willChecksumBeCorrected ?? false;
        $this->isCVNCorrectionPossible = $data->isCVNCorrectionPossible ?? false;
        $this->isChecksumCorrectionNeeded = $data->isChecksumCorrectionNeeded ?? false;
        $this->information = $data->information ?? null;
    }

    private function convertDataFromCallback($data): \stdClass
    {
        $newData = new \stdClass();
        $newData->kess3FileSlotGUID = $data->kess3FileSlotGUID ?? '';
        $newData->name = $data->Name ?? '';
        $newData->readFileURL = $data->ReadFileURL ?? '';
        $newData->kess3Mode = $data->kess3Mode ?? '';
        $newData->idFileURL = $data->idFileURL ?? '';
        $newData->obdDecodedFileURL = $data->obdDecodedFileURL ?? '';
        $newData->bootBenchComponents = $this->convertBootBenchComponents( $data->BootBenchComponents);
        $newData->wasOriginalFileFoundThroughVirtualReading = $data->WasOriginalFileFoundThroughVirtualReading ?? false;
        $newData->isOriginalFileNeeded = $data->IsOriginalFileNeeded ?? false;
        $newData->willChecksumBeCorrected = $data->WillChecksumBeCorrected ?? false;
        $newData->isChecksumCorrectionNeeded = $data->IsChecksumCorrectionNeeded ?? false;
        $newData->isCVNCorrectionPossible = $data->IsCVNCorrectionPossible ?? false;
        $newData->information = $data->Information ?? null;
        return $newData;
    }

    private function convertBootBenchComponents($data): array
    {
        $result = [];
        if (is_array($data) && !empty($data)) {
            foreach($data as $fileInfo) {
                $newFile = new \stdClass();
                $newFile->type = $fileInfo->Type;
                $newFile->decodedFileURL = $fileInfo->DecodedFileURL;
                $result[] = $newFile;
            }
        }
        return $result;
    }

    public static function createEmpty()
    {
        return new AsyncOperationResultDto([]);
    }

    public function toJson()
    {
        return $this->jsonData;
    }

    public function isObd()
    {
        return $this->kess3Mode === self::TYPE_OBD;
    }

    public function isBootBench()
    {
        return $this->kess3Mode === self::TYPE_BOOT_BENCH;
    }

    public function createEncodeRequestFieldByType($fileType)
    {
        switch ($fileType) {
            case 'BootBenchDecodedMicro':
                return 'microFileGUID';
                break;
            case 'BootBenchDecodedFlash':
                return 'flashFileGUID';
                break;
            case 'BootBenchDecodedEEPROM':
                return 'eepromFileGUID';
                break;
            case 'BootBenchDecodedMapFile':
                return 'mapFileFileGUID';
                break;
        }
    }

    public function createEncodeFieldByDecodedType($component)
    {
        switch ($component) {
            case 'BootBenchDecodedMicro':
                return 'microFileGUID';
                break;
            case 'BootBenchDecodedFlash':
                return 'flashFileGUID';
                break;
            case 'BootBenchDecodedEEPROM':
                return 'eepromFileGUID';
                break;
            case 'BootBenchDecodedMapFile':
                return 'mapFileFileGUID';
                break;
        }
    }

    public function generateEncodeRequestDataKeysArray(array $files): array
    {
        $data = [];
        if ($this->isObd()) {
            if ($this->isCVNCorrectionPossible()) {
                $data[] = 'willCorrectCVN';
            }
            if ($this->isOriginalFileNeeded()) {
                $data[] = 'originalFileGUID';
            }
            $data[] = 'modifiedFileGUID';
        } elseif ($this->isBootBench()) {
            foreach ($files as $file) {
                $params = json_decode($file->params);
                if (isset($params->fileType)) {
                    $data[] = $this->createEncodeRequestFieldByType($params->fileType);
                }
            }

//            if (!empty($this->bootBenchComponents)) {
//                foreach($this->bootBenchComponents as $component) {
//                    $data[] = $this->createEncodeRequestFieldByType($component);
//                }
//            }
        }
        return $data;
    }

    public function fillEncodeRequestDataArray(array &$encodeRequestData, array $files): array
    {
        $result = ['error' => false, 'data' => $encodeRequestData];
        if ($this->isObd()) {
            $file = $files[0];

            $file_history = json_decode($file->file_history);
            $params = json_decode($file->params);
            if (!isset($file_history->guid)) {
                $result['error'] = true;
                return $result;
            }
            $encodeRequestData = array_flip($encodeRequestData);
            $encodeRequestData['modifiedFileGUID'] = $file_history->guid;
            if ($this->isOriginalFileNeeded()) {
                $encodeRequestData['originalFileGUID'] = $params->guid;
            }

            $result['data'] = $encodeRequestData;

            return $result;

        } elseif ($this->isBootBench()) {
            $encodeRequestData = array_flip($encodeRequestData);
            foreach ($files as $file) {
                $file_history = json_decode($file->file_history);
                $params = json_decode($file->params);
                if (!isset($file_history->guid)) {
                    $result['error'] = true;
                    continue;
                }


                $encodeRequestData[$this->createEncodeFieldByDecodedType($params->fileType)] = $file_history->guid;

            }
            $result['data'] = $encodeRequestData;

            return $result;
        }
    }


    public function generateDecodedFileUrls(): array
    {
        $urls = [];
        if ($this->isObd()) {
            $urls[] = $this->obdDecodedFileURL;
        } elseif ($this->isBootBench()) {
            if (!empty($this->bootBenchComponents)) {
                foreach($this->bootBenchComponents as $component){
                    $urls[$component->type] = $component->decodedFileURL;
                }
            }
        }
        return $urls;
    }

    public function generateEncodeUrl(): string
    {
        $url = '';
        if ($this->isObd()) {
            $url = '/encode-obd-file';
        } elseif ($this->isBootBench()) {
            $url = '/encode-boot-bench-file';
        }
        return $url;
    }


    /**
     * @return string
     */
    public function getKess3FileSlotGUID(): string
    {
        return $this->kess3FileSlotGUID;
    }

    /**
     * @param string $kess3FileSlotGUID
     */
    public function setKess3FileSlotGUID(string $kess3FileSlotGUID): void
    {
        $this->kess3FileSlotGUID = $kess3FileSlotGUID;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName(string $name): void
    {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getReadFileURL(): string
    {
        return $this->readFileURL;
    }

    /**
     * @param string $readFileURL
     */
    public function setReadFileURL(string $readFileURL): void
    {
        $this->readFileURL = $readFileURL;
    }

    /**
     * @return string
     */
    public function getKess3Mode(): string
    {
        return $this->kess3Mode;
    }

    /**
     * @param string $kess3Mode
     */
    public function setKess3Mode(string $kess3Mode): void
    {
        $this->kess3Mode = $kess3Mode;
    }

    /**
     * @return string
     */
    public function getIdFileURL(): string
    {
        return $this->idFileURL;
    }

    /**
     * @param string $idFileURL
     */
    public function setIdFileURL(string $idFileURL): void
    {
        $this->idFileURL = $idFileURL;
    }

    /**
     * @return string
     */
    public function getObdDecodedFileURL(): string
    {
        return $this->obdDecodedFileURL;
    }

    /**
     * @param string $obdDecodedFileURL
     */
    public function setObdDecodedFileURL(string $obdDecodedFileURL): void
    {
        $this->obdDecodedFileURL = $obdDecodedFileURL;
    }

    /**
     * @return array
     */
    public function getBootBenchComponents(): array
    {
        return $this->bootBenchComponents;
    }

    /**
     * @param array $bootBenchComponents
     */
    public function setBootBenchComponents(array $bootBenchComponents): void
    {
        $this->bootBenchComponents = $bootBenchComponents;
    }

    /**
     * @return bool
     */
    public function isWasOriginalFileFoundThroughVirtualReading(): bool
    {
        return $this->wasOriginalFileFoundThroughVirtualReading;
    }

    /**
     * @param bool $wasOriginalFileFoundThroughVirtualReading
     */
    public function setWasOriginalFileFoundThroughVirtualReading(bool $wasOriginalFileFoundThroughVirtualReading): void
    {
        $this->wasOriginalFileFoundThroughVirtualReading = $wasOriginalFileFoundThroughVirtualReading;
    }

    /**
     * @return bool
     */
    public function isOriginalFileNeeded(): bool
    {
        return $this->isOriginalFileNeeded;
    }

    /**
     * @param bool $isOriginalFileNeeded
     */
    public function setIsOriginalFileNeeded(bool $isOriginalFileNeeded): void
    {
        $this->isOriginalFileNeeded = $isOriginalFileNeeded;
    }

    /**
     * @return bool
     */
    public function isWillChecksumBeCorrected(): bool
    {
        return $this->willChecksumBeCorrected;
    }

    /**
     * @param bool $willChecksumBeCorrected
     */
    public function setWillChecksumBeCorrected(bool $willChecksumBeCorrected): void
    {
        $this->willChecksumBeCorrected = $willChecksumBeCorrected;
    }

    /**
     * @return bool
     */
    public function isCVNCorrectionPossible(): bool
    {
        return $this->isCVNCorrectionPossible;
    }

    /**
     * @param bool $isCVNCorrectionPossible
     */
    public function setIsCVNCorrectionPossible(bool $isCVNCorrectionPossible): void
    {
        $this->isCVNCorrectionPossible = $isCVNCorrectionPossible;
    }

    /**
     * @return null
     */
    public function getInformation()
    {
        return $this->information;
    }

    /**
     * @param null $information
     */
    public function setInformation($information): void
    {
        $this->information = $information;
    }

    /**
     * @return string
     */
    public function getJsonData(): string
    {
        return $this->jsonData;
    }

    /**
     * @param string $jsonData
     */
    public function setJsonData(string $jsonData): void
    {
        $this->jsonData = $jsonData;
    }

    /**
     * @return bool
     */
    public function isChecksumCorrectionNeeded(): bool
    {
        return $this->isChecksumCorrectionNeeded;
    }

    /**
     * @param bool $isChecksumCorrectionNeeded
     */
    public function setIsChecksumCorrectionNeeded(bool $isChecksumCorrectionNeeded): void
    {
        $this->isChecksumCorrectionNeeded = $isChecksumCorrectionNeeded;
    }


}