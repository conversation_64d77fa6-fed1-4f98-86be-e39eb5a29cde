<?php

namespace common\helpers;

use common\models\ProjectMessages;
use common\models\User;
use common\models\UserMessagesUsers;
use backend\modules\ticket\models\TicketHead;
use Yii;
use yii\helpers\Url;
use yii\httpclient\Client;

class MessageHelper
{
    const TYPE_USUAL = 1; // уведомление в нотах, отправлять в мессенджеры и смс не требуется
    const TYPE_SYS = 2; // системное уведомление, отправлять в мессенджеры и смс не требуется
    const TYPE_NOTE = 3; // отправляется в смс и мессенджерах
    const TYPE_RELOAD = 4; // уведомление для перезагрузки страницы при наличии, никуда никому не отправляется, гасится при обновлении страницы

    const VALUE_NOT = 0; // уведомление в нотах, отправлять в мессенджеры и смс не требуется
    const VALUE_YES = 1; // уведомление в системных нотах в проект, видны только мастеру, отправлять в мессенджеры и смс не требуется
    const TO_ADMIN = 'admin'; // уведомление мастеру или мастерам
    const VALUE_RELOAD = 'Reload'; // уведомление мастеру или мастерам
    const ADMIN_USER_ID = 1;
    const ALIENTECH_USER_ID = 99;
    const AUTOPACK_USER_ID = 100;

    public static function sendMailMessage($message = null, $sender = null, $recipient = null, $projectMessageSend = null)
    {
        if ($message && $sender && $recipient && $projectMessageSend) {
            Yii::$app->mailer->compose()
                ->setFrom(Yii::$app->params['adminEmail'])
                ->setTo($recipient->email)
                ->setSubject($message->title)
                ->setTextBody($message->comment)
                ->setHtmlBody('<b>'.$message->comment.'</b>')
                ->send();
            $projectMessageSend->setAttribute('mail_sended', 1);
            $projectMessageSend->save(false);
        }
    }

    public static function sendTelegramMessage($message = null, $sender = null, $recipient = null, $projectMessageSend = null)
    {
        if ($message && $sender && $recipient && $projectMessageSend && isset(Yii::$app->telegram) && !YII_ENV_DEV ) {
            $tgChatArray = [];
            if ($recipient->tg_chat_id) {
                $tgChatArray[] = $recipient->tg_chat_id;
            }
            if (!is_null($recipient->tgAccounts) && count($recipient->tgAccounts) > 0) {
                foreach ($recipient->tgAccounts as $tgAccount) {
                    if (!empty($tgAccount->chat_id)) {
                        $tgChatArray[] = $tgAccount->chat_id;
                    }
                }
            }
            $tgChatArray = array_unique($tgChatArray);
            foreach ($tgChatArray as $i => $tgChat) {
                Yii::$app->telegram->sendMessage([
                    'chat_id' => $tgChat,
                    'text' => $message->comment,
                ]);
                $projectMessageSend->setAttribute('telegram_sended', $i+1);
                $projectMessageSend->save(false);
            }
        }
    }

    public static function sendSms($message = null, $sender = null, $recipient = null, $projectMessageSend = null)
    {
//        https://pbx.msgroup.ua/api/smssend.php?login=msua&password=hJw1an263&command=send&from=MasterServ&to=************&source=site&user=chiptuning-ms.business&message=Hello,%20World
        if ($message && $sender && $recipient && $projectMessageSend && !empty($recipient->phone)) {
            $client = new Client();
            $response = $client->createRequest()
                ->setMethod('GET')
                ->setFormat(Client::FORMAT_URLENCODED)
                ->setUrl('https://pbx.msgroup.ua/api/smssend.php')
                ->setData([
                    'login' => 'msua',
                    'password' => 'hJw1an263',
                    'command' => 'send',
                    'from' => 'MasterServ',
                    'to' => $recipient->phone,
                    'source' => 'site',
                    'user' => 'chiptuning-ms.business',
                    'message' => $message->content,
                ])
                ->send();
            if ($response->isOk) {
                $projectMessageSend->setAttribute('sms_sended', 1);
                $projectMessageSend->save(false);
            }
        }
    }

    private static function logMessage($message = null, $sender = null, $recipient = null)
    {
        if ($message && $sender && $recipient) {
            Yii::debug($message->title.''.$message->comment .''.$message->content  );
        }

    }

    public static function send(\common\models\ProjectMessagesSend $projectMessageSend)
    {
        $message = ProjectMessages::findOne($projectMessageSend->message_id);
        $user = \Yii::$app->user ?? null;
        $sender = null;
        $recipient = null;
        if (Yii::$app->id == 'console') {
//          Алиентеч запаковал файл в проекте, отправка уведомления создателю проекта
//              Отправитель будет админ сайта
                $sender = User::findOne(1);
                $recipient = $message->project->creator;
        } else {
            if (!is_null($user) && isset($message->project) && $message->project->created_by !== $user->id) {
//          Действие производил не создатель проекта, отправка уведомления создателю проекта
//              Отправитель будет админ сайта
                $sender = User::findOne(1);
                $recipient = $message->project->creator;

//                if ($user->can('manager')) {
//              Проект создал не менеджер, отправка уведомления создателю проекта
//            } else {
////          Пользователь
//                $sender = $message->project->creator;
//                $recipient = User::findOne(1);
//            }

            } else {

//          Действие производил создатель проекта, отправка уведомления администрации
                $sender = $message->project->creator;
                $recipient = User::findOne(1);

            }
        }
        self::logMessage($message, $sender, $recipient, $projectMessageSend);
        if (!is_null($sender) || !is_null($recipient)) {
            Yii::error('Отправляем уведомление, нет получателя или отправителя');
            return;
        }
        if ($projectMessageSend->sms) {
            self::sendSms($message, $sender, $recipient, $projectMessageSend);
        }
        if ($projectMessageSend->mail && ($recipient->send_email == 1)) {
            self::sendMailMessage($message, $sender, $recipient, $projectMessageSend);
        }
        if ($projectMessageSend->telegram) {
            self::sendTelegramMessage($message, $sender, $recipient, $projectMessageSend);
        }
    }

    public static function sendMessageToUser($user_id, $message_id)
    {
        $userMessageUser = new UserMessagesUsers();
        $userMessageUser->setAttributes([
            'user_id' => $user_id,
            'message_id' => $message_id
        ]);
        $userMessageUser->save(false);
    }

    public static function getMessages()
    {
        // выборка сообщений, созданных не текущим пользователем
        $q = ProjectMessages::find();
//        $q->where(['!=', 'project_messages.created_by', Yii::$app->user->identity->id]);
        if (!Yii::$app->user->can('administrator')) {
//                если текущий пользователь не тюнер - выбираем сообщения только по его проектам
            $q->joinWith(['project' => function ($q) {
                $q->where(['projects.created_by' => Yii::$app->user->identity->id]);
            }]);
            $q->andWhere(['OR', ['IS', 'send_to', null], ['send_to' => Yii::$app->user->identity->id]]);
        } else {
            $q->andWhere(['project_messages.send_to' => 'admin']);
        }
//                выбираем 5 еще не просмотренных сообщений
        $q->andWhere(['project_messages.is_shown' => 0]);
            $q->andWhere(['project_messages.type' => MessageHelper::TYPE_NOTE]);
            $q->orderBy('id asc');
//            $q->andWhere(['project_messages.sys' => 0]);
//        $q->limit(15);
//            print_r($q->createCommand()->getRawSql());
//            die;
        $command = $q->createCommand()->getRawSql();
            $messages = $q->all();

            return $messages;
    }

    public static function getSupportMessages()
    {
        $tickets = [];
        if (Yii::$app->user->can('administrator')) {
            $tickets = TicketHead::find()->where('user_id != '.Yii::$app->user->identity->id)->andWhere(['status' => TicketHead::WAIT])->all();
//            $tickets = Yii::$app->db->createCommand("SELECT * FROM ticket_head WHERE status IN (0,4) AND user_id !=".)->queryAll();
        } else {
            $tickets = TicketHead::findAll(['user_id' => Yii::$app->user->identity->id, 'status' => TicketHead::ANSWER]);
            //            $tickets = Yii::$app->db->createCommand("SELECT * FROM ticket_head WHERE user_id !=".Yii::$app->user->identity->id)->queryAll();
        }

//        $tickets =
        // выборка сообщений, созданных не текущим пользователем
//        $q = ProjectMessages::find()->where(['!=', 'project_messages.created_by', Yii::$app->user->identity->id]);
//        if (!Yii::$app->user->can('administrator')) {
//            //                если текущий пользователь не тюнер - выбираем сообщения только по его проектам
//            $q->joinWith(['project' => function ($q) {
//                $q->where(['projects.created_by' => Yii::$app->user->identity->id]);
//            }]);
//        }
//        //                выбираем 5 еще не просмотренных сообщений
//        $q->andWhere(['project_messages.is_shown' => 0]);
//        $q->andWhere(['project_messages.type' => MessageHelper::TYPE_NOTE]);
//        $q->orderBy('id asc');
//        //            $q->andWhere(['project_messages.sys' => 0]);
//        //        $q->limit(15);
//        //            print_r($q->createCommand()->getRawSql());
//        //            die;
//        $messages = $q->all();
//if (Yii::$app->request->get('test')) {
    return $tickets;
//    print_r($tickets); die;
//}
        return [];
    }

    public static function checkReloadMessages()
    {
        // выборка сообщений, которые сообщают, нужно ли обновить страницу, созданных не текущим пользователем
        $q = ProjectMessages::find();
//        $q->where(['!=', 'project_messages.created_by', Yii::$app->user->identity->id]);
        if (!Yii::$app->user->can('manager')) {
//                если текущий пользователь не тюнер - выбираем сообщения только по его проектам
            $q->joinWith(['project' => function ($q) {
                $q->where(['projects.created_by' => Yii::$app->user->identity->id]);
            }]);
            $q->andWhere(['OR','send_to IS NULL', 'send_to' => Yii::$app->user->identity->id]);
        } else {
            $q->andWhere(['project_messages.send_to' => 'admin']);
        }
//                выбираем 5 еще не просмотренных сообщений
        $q->andWhere(['project_messages.is_shown' => 0]);
        $q->andWhere(['project_messages.type' => MessageHelper::TYPE_RELOAD]);
//            $q->andWhere(['project_messages.sys' => 0]);
//        $q->limit(15);
//            print_r($q->createCommand()->getRawSql());
//            die;
        $messages = $q->all();

        return $messages;

    }

    public static function sendSimplyTelegramMessage($messageText = '', $sender = null, $recipient = null)
    {
        if ($messageText && $sender && $recipient) {
            Yii::$app->telegram->sendMessage([
                'chat_id' => $recipient->tg_chat_id,
                'text' => $messageText,
            ]);
        }
    }

    public static function getAdministrators()
    {
        return Yii::$app->authManager->getUserIdsByRole('administrator');
    }

}
