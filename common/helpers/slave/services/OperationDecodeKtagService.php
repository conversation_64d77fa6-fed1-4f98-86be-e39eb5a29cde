<?php


namespace common\helpers\slave\services;



use common\helpers\MessageHelper;
use common\helpers\ProjectHelper;
use common\models\ProjectFiles;
use Yii;

class OperationDecodeKtagService extends OperationKtagService
{

    public function __construct($fileObject)
    {
        $this->fileObject = $fileObject;
        $this->projectFile = $this->fileObject->getProjectFile();
        $this->slaveFile = $this->fileObject->getSlaveFile();
    }

    protected function processOperation(): ?bool
    {
        $this->log('processOperation////');

        if (is_null(parent:: processOperation())) {
            return null;
        }

        $this->slotGuid = $this->slaveOperation->slotGUID;

        if ($this->slaveOperation->result->components) {
            foreach ($this->slaveOperation->result->components as $key => $filePart) {
                $fileData = $this->downloadFile($filePart->decodedFileURL);
                $result = self::saveFileToProject($fileData, $filePart->type, $key);
                if (is_null($result)) {
                    return null;
                }
            }
        }

        return true;
    }

    protected function startMainMethod(){
        $this->log('startMainMethod////');
        $this->log('////startMainMethod');
        return $this->decodeFile();
    }

    private function saveFileToProject($fileData, $filePartType, $key): ?bool
    {
        $this->log('saveFileToProject////');

        $fileSuffix = '_DECODED';

        $fileName = $this->slaveOperation->result->name.$fileSuffix.'_'.$filePartType;

        $filePath = Yii::getAlias('@storage') . '/web/projects/files/' . $fileName;

        if (!$this->saveFileToStorage($filePath, $fileData->data))
        {
            $this->addError('saveFileToStorage_false');
            $this->log($this->getErrors());
            return null;
        }

        $projectFile = new ProjectFiles();

        $projectFile->setAttributes([
            'orig' => ProjectHelper::PROJECT_FILE_ORIGINAL,
            'type' => 'external',
            'title' => $fileName,
            'filename' => $fileName,
            'component_name' => $filePartType ?? null,
            'project_id' => $this->projectFile->project_id,
            'file_id' => $this->projectFile->id,
            'alientech_operation_id' => $this->slaveOperation->getOperationModel()->id,
            'file_type' => ProjectHelper::FILE_TYPE_ORIGINAL_DECODED,
            'path' => $filePath,
            'hash' => Yii::$app->security->generateRandomString(12),
        ]);

        $messageStart = 'Decoded file downloaded ';

        if (!$projectFile->save(false)) {
            $this->addError('save_projectFile_false');
            $this->addError($projectFile->errors);
            $this->log($this->getErrors());
            return null;
        }

        if ($key > 0) {
            return true;
        }

        if (!$this->setMessagesForDecodedFile($projectFile, $messageStart))
        {
            $this->addError('saveFileToStorage_false');
            $this->log($this->getErrors());
            return null;
        }

        return true;
    }

}
