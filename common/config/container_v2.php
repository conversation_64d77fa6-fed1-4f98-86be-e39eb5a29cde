<?php

declare(strict_types=1);

use common\chip\event\EventDispatcher;
use common\chip\externalIntegrations\kess3\Application\Factory\DecodingEventFactory;
use common\chip\externalIntegrations\kess3\Application\Handler\ProcessDecodingResultHandler;
use common\chip\externalIntegrations\kess3\Application\Handler\StartDecodingHandler;
use common\chip\externalIntegrations\kess3\Application\Service\Kess3DecodingFacade;
use common\chip\externalIntegrations\kess3\Application\Validator\StartDecodingCommandValidator;
use common\chip\externalIntegrations\kess3\Domain\Repository\DecodingOperationRepositoryInterface;
use common\chip\externalIntegrations\kess3\Domain\Service\DecodingDomainService;
use common\chip\externalIntegrations\kess3\Infrastructure\Adapter\LegacyKess3ServiceAdapter;
use common\chip\externalIntegrations\kess3\Infrastructure\Console\DecodingConsoleController;
use common\chip\externalIntegrations\kess3\Infrastructure\EventHandler\DecodingEventHandler;
use common\chip\externalIntegrations\kess3\Infrastructure\ExternalService\AlientechApiClient;
use common\chip\externalIntegrations\kess3\Infrastructure\ExternalService\AlientechApiClientInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Repository\DecodingOperationRepository;
use common\chip\externalIntegrations\kess3\Interfaces\Http\DecodingController;
use common\chip\alientech\services\AlientechLinkService;
use common\chip\alientech\services\AlientechService;
use common\chip\alientech\services\FileSlotService;
use common\chip\alientech\services\LogService;
use common\chip\project\services\MessageService;

/**
 * Полная конфигурация DI контейнера для системы декодирования Kess3
 * Версия 2.0 с прямым созданием экземпляров классов
 */
return [
    // ========================================
    // INTERFACES & ABSTRACTIONS
    // ========================================

    DecodingOperationRepositoryInterface::class => function() {
        return new DecodingOperationRepository();
    },

    AlientechApiClientInterface::class => function() {
        $linkService = new AlientechLinkService();
        $alientechService = new AlientechService();
        $logService = new LogService();
        $fileSlotService = new FileSlotService(
            linkService: $linkService,
            logService: $logService
        );

        return new AlientechApiClient(
            linkService: $linkService,
            alientechService: $alientechService,
            fileSlotService: $fileSlotService
        );
    },

    // ========================================
    // DOMAIN LAYER
    // ========================================

    DecodingDomainService::class => function() {
        $repository = new DecodingOperationRepository();

        return new DecodingDomainService(
            repository: $repository
        );
    },

    // ========================================
    // APPLICATION LAYER
    // ========================================

    // Command Handlers
    StartDecodingHandler::class => function() {
        $repository = new DecodingOperationRepository();
        $domainService = new DecodingDomainService(repository: $repository);

        $linkService = new AlientechLinkService();
        $alientechService = new AlientechService();
        $logService = new LogService();
        $fileSlotService = new FileSlotService(
            linkService: $linkService,
            logService: $logService
        );
        $apiClient = new AlientechApiClient(
            linkService: $linkService,
            alientechService: $alientechService,
            fileSlotService: $fileSlotService
        );

        $eventDispatcher = \common\chip\event\EventSystemBootstrap::getDispatcher();

        return new StartDecodingHandler(
            domainService: $domainService,
            repository: $repository,
            apiClient: $apiClient,
            eventDispatcher: $eventDispatcher
        );
    },

    ProcessDecodingResultHandler::class => function() {
        $repository = new DecodingOperationRepository();
        $eventDispatcher = \common\chip\event\EventSystemBootstrap::getDispatcher();

        return new ProcessDecodingResultHandler(
            repository: $repository,
            eventDispatcher: $eventDispatcher
        );
    },

    // Application Services
    Kess3DecodingFacade::class => function() {
        $repository = new DecodingOperationRepository();
        $domainService = new DecodingDomainService(repository: $repository);

        $linkService = new AlientechLinkService();
        $alientechService = new AlientechService();
        $logService = new LogService();
        $fileSlotService = new FileSlotService(
            linkService: $linkService,
            logService: $logService
        );
        $apiClient = new AlientechApiClient(
            linkService: $linkService,
            alientechService: $alientechService,
            fileSlotService: $fileSlotService
        );

        $eventDispatcher = \common\chip\event\EventSystemBootstrap::getDispatcher();

        $startDecodingHandler = new StartDecodingHandler(
            domainService: $domainService,
            repository: $repository,
            apiClient: $apiClient,
            eventDispatcher: $eventDispatcher
        );

        $processResultHandler = new ProcessDecodingResultHandler(
            repository: $repository,
            eventDispatcher: $eventDispatcher
        );

        return new Kess3DecodingFacade(
            startDecodingHandler: $startDecodingHandler,
            processResultHandler: $processResultHandler,
            domainService: $domainService,
            repository: $repository
        );
    },

    // Factories
    DecodingEventFactory::class => function() {
        return new DecodingEventFactory();
    },

    // Validators
    StartDecodingCommandValidator::class => function() {
        return new StartDecodingCommandValidator();
    },

    // ========================================
    // INFRASTRUCTURE LAYER
    // ========================================

    // Repositories
    DecodingOperationRepository::class => function() {
        return new DecodingOperationRepository();
    },

    // External Services
    AlientechApiClient::class => function() {
        $linkService = new AlientechLinkService();
        $alientechService = new AlientechService();
        $logService = new LogService();
        $fileSlotService = new FileSlotService(
            linkService: $linkService,
            logService: $logService
        );

        return new AlientechApiClient(
            linkService: $linkService,
            alientechService: $alientechService,
            fileSlotService: $fileSlotService
        );
    },

    // Event Handlers
    DecodingEventHandler::class => function() {
        $linkService = new AlientechLinkService();
        $alientechService = new AlientechService();
        $logService = new LogService();
        $fileSlotService = new FileSlotService(
            linkService: $linkService,
            logService: $logService
        );
        $apiClient = new AlientechApiClient(
            linkService: $linkService,
            alientechService: $alientechService,
            fileSlotService: $fileSlotService
        );
        $messageService = new MessageService();

        return new DecodingEventHandler($apiClient, $messageService);
    },

    // Console Controllers
    DecodingConsoleController::class => function() {
        $repository = new DecodingOperationRepository();
        $domainService = new DecodingDomainService(repository: $repository);

        $linkService = new AlientechLinkService();
        $alientechService = new AlientechService();
        $logService = new LogService();
        $fileSlotService = new FileSlotService(
            linkService: $linkService,
            logService: $logService
        );
        $apiClient = new AlientechApiClient(
            linkService: $linkService,
            alientechService: $alientechService,
            fileSlotService: $fileSlotService
        );

        $eventDispatcher = \common\chip\event\EventSystemBootstrap::getDispatcher();

        $startDecodingHandler = new StartDecodingHandler(
            domainService: $domainService,
            repository: $repository,
            apiClient: $apiClient,
            eventDispatcher: $eventDispatcher
        );

        $processResultHandler = new ProcessDecodingResultHandler(
            repository: $repository,
            eventDispatcher: $eventDispatcher
        );

        $decodingFacade = new Kess3DecodingFacade(
            startDecodingHandler: $startDecodingHandler,
            processResultHandler: $processResultHandler,
            domainService: $domainService,
            repository: $repository
        );

        return new DecodingConsoleController(
            decodingFacade: $decodingFacade
        );
    },

    // Adapters
    LegacyKess3ServiceAdapter::class => function() {
        $repository = new DecodingOperationRepository();
        $domainService = new DecodingDomainService(repository: $repository);

        $linkService = new AlientechLinkService();
        $alientechService = new AlientechService();
        $logService = new LogService();
        $fileSlotService = new FileSlotService(
            linkService: $linkService,
            logService: $logService
        );
        $apiClient = new AlientechApiClient(
            linkService: $linkService,
            alientechService: $alientechService,
            fileSlotService: $fileSlotService
        );

        $eventDispatcher = \common\chip\event\EventSystemBootstrap::getDispatcher();

        $startDecodingHandler = new StartDecodingHandler(
            domainService: $domainService,
            repository: $repository,
            apiClient: $apiClient,
            eventDispatcher: $eventDispatcher
        );

        $processResultHandler = new ProcessDecodingResultHandler(
            repository: $repository,
            eventDispatcher: $eventDispatcher
        );

        $decodingFacade = new Kess3DecodingFacade(
            startDecodingHandler: $startDecodingHandler,
            processResultHandler: $processResultHandler,
            domainService: $domainService,
            repository: $repository
        );

        return new LegacyKess3ServiceAdapter(
            decodingFacade: $decodingFacade
        );
    },

    // ========================================
    // LEGACY SERVICES (Backward Compatibility)
    // ========================================

    AlientechLinkService::class => function() {
        return new AlientechLinkService();
    },

    AlientechService::class => function() {
        return new AlientechService();
    },

    FileSlotService::class => function() {
        $linkService = new AlientechLinkService();
        $logService = new LogService();

        return new FileSlotService(
            linkService: $linkService,
            logService: $logService
        );
    },

    LogService::class => function() {
        return new LogService();
    },

    MessageService::class => function() {
        return new MessageService();
    },

    // ========================================
    // EVENT SYSTEM INTEGRATION
    // ========================================

    EventDispatcher::class => function() {
        // Используем существующий диспетчер событий из системы
        return \common\chip\event\EventSystemBootstrap::getDispatcher();
    },
];
