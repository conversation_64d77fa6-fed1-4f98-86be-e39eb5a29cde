<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */return [
'API access token' => 'Jeton d\'accès à l\'API',
'Email' => 'Email',
'Expire At' => 'Expire à',
'Not Active' => 'Pas actif',
'Password' => 'Mot de passe',
'Roles' => 'Rôles',
'Token' => 'Jeton',
'&quot;{attribute}&quot; must be a valid JSON' => '& quot; {attribut} & quot; doit être un JSON valide',
'Active' => 'actif',
'Article ID' => 'ID d\'article',
'Article View' => 'Article View',
'Author' => 'Auteur',
'Base URL' => 'URL de base',
'Base Url' => 'URL de base',
'Body' => 'Corps',
'Caption' => 'Légende',
'Carousel ID' => 'ID de carrousel',
'Category' => 'Catégorie',
'Comment' => 'Commentaire',
'Component' => 'Composant',
'Config' => 'Configuration',
'Created At' => 'Créé à',
'Created at' => 'Créé à',
'Deleted' => 'Supprimé',
'Down to maintenance.' => 'Jusqu\'à l\'entretien.',
'E-mail' => 'Email',
'File Type' => 'Type de fichier',
'Firstname' => 'Prénom',
'Gender' => 'sexe',
'ID' => 'ID',
'Image' => 'Image',
'Key' => 'Clé',
'Last login' => 'Dernière connexion',
'Lastname' => 'Nom de famille',
'Locale' => 'Langue',
'Middlename' => 'Deuxième nom',
'Name' => 'prénom',
'Order' => 'Ordre',
'Page View' => 'Vue de page',
'Parent Category' => 'Catégorie Parentale',
'Path' => 'Chemin',
'Picture' => 'Image',
'Published' => 'Publié',
'Published At' => 'Publié à',
'Size' => 'Taille',
'Slug' => 'Limace',
'Status' => 'Statut',
'Thumbnail' => 'La vignette',
'Title' => 'Titre',
'Type' => 'Type',
'Updated At' => 'Mis à jour à',
'Updated at' => 'Mis à jour à',
'Updater' => 'Modificateur',
'Upload Ip' => 'Télécharger Ip',
'Url' => 'URL',
'User ID' => 'Identifiant d\'utilisateur',
'Username' => 'Nom d\'utilisateur',
'Value' => 'Valeur',
];
