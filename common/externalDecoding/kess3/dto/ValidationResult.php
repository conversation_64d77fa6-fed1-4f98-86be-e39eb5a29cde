<?php

namespace common\externalDecoding\kess3\dto;

/**
 * DTO для результата валидации
 * 
 * Содержит информацию о результате валидации операции или данных
 */
class ValidationResult
{
    public function __construct(
        private bool $isValid,
        private array $errors = [],
        private array $warnings = [],
        private array $metadata = []
    ) {}

    public function isValid(): bool
    {
        return $this->isValid;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    public function hasWarnings(): bool
    {
        return !empty($this->warnings);
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getWarnings(): array
    {
        return $this->warnings;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public function addError(string $field, string $message): self
    {
        $this->errors[$field][] = $message;
        $this->isValid = false;
        return $this;
    }

    public function addWarning(string $field, string $message): self
    {
        $this->warnings[$field][] = $message;
        return $this;
    }

    public function setMetadata(string $key, mixed $value): self
    {
        $this->metadata[$key] = $value;
        return $this;
    }

    public function getFirstError(): ?string
    {
        if (empty($this->errors)) {
            return null;
        }

        $firstField = array_key_first($this->errors);
        return $this->errors[$firstField][0] ?? null;
    }

    public function getAllMessages(): array
    {
        $messages = [];
        
        foreach ($this->errors as $field => $fieldErrors) {
            foreach ($fieldErrors as $error) {
                $messages[] = "Error in {$field}: {$error}";
            }
        }
        
        foreach ($this->warnings as $field => $fieldWarnings) {
            foreach ($fieldWarnings as $warning) {
                $messages[] = "Warning in {$field}: {$warning}";
            }
        }
        
        return $messages;
    }

    /**
     * Создает успешный результат валидации
     */
    public static function valid(array $metadata = []): self
    {
        return new self(true, [], [], $metadata);
    }

    /**
     * Создает неуспешный результат валидации
     */
    public static function invalid(array $errors = [], array $warnings = [], array $metadata = []): self
    {
        return new self(false, $errors, $warnings, $metadata);
    }

    /**
     * Создает результат с одной ошибкой
     */
    public static function withError(string $field, string $message, array $metadata = []): self
    {
        return new self(false, [$field => [$message]], [], $metadata);
    }

    /**
     * Создает результат с одним предупреждением
     */
    public static function withWarning(string $field, string $message, array $metadata = []): self
    {
        return new self(true, [], [$field => [$message]], $metadata);
    }

    /**
     * Объединяет несколько результатов валидации
     */
    public static function merge(ValidationResult ...$results): self
    {
        $isValid = true;
        $errors = [];
        $warnings = [];
        $metadata = [];

        foreach ($results as $result) {
            if (!$result->isValid()) {
                $isValid = false;
            }

            foreach ($result->getErrors() as $field => $fieldErrors) {
                $errors[$field] = array_merge($errors[$field] ?? [], $fieldErrors);
            }

            foreach ($result->getWarnings() as $field => $fieldWarnings) {
                $warnings[$field] = array_merge($warnings[$field] ?? [], $fieldWarnings);
            }

            $metadata = array_merge($metadata, $result->getMetadata());
        }

        return new self($isValid, $errors, $warnings, $metadata);
    }

    /**
     * Преобразует в массив
     */
    public function toArray(): array
    {
        return [
            'isValid' => $this->isValid,
            'errors' => $this->errors,
            'warnings' => $this->warnings,
            'metadata' => $this->metadata,
        ];
    }
}
