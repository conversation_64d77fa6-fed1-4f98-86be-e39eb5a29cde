<?php

namespace common\externalDecoding\kess3\dto;

use common\chip\alientech\entities\dto\AsyncOperationDto;

/**
 * DTO для результата выполнения операции
 * 
 * Унифицированный результат для всех типов операций
 */
class OperationResult
{
    public const STATUS_SUCCESS = 'success';
    public const STATUS_FAILED = 'failed';
    public const STATUS_PENDING = 'pending';
    public const STATUS_CANCELLED = 'cancelled';

    public function __construct(
        private string $status,
        private ?AsyncOperationDto $operation = null,
        private ?string $operationId = null,
        private ?array $data = null,
        private ?string $error = null,
        private array $metadata = [],
        private ?\DateTimeImmutable $timestamp = null
    ) {
        $this->timestamp = $this->timestamp ?? new \DateTimeImmutable();
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function isSuccess(): bool
    {
        return $this->status === self::STATUS_SUCCESS;
    }

    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    public function isCancelled(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    public function getOperation(): ?AsyncOperationDto
    {
        return $this->operation;
    }

    public function setOperation(?AsyncOperationDto $operation): self
    {
        $this->operation = $operation;
        if ($operation && !$this->operationId) {
            $this->operationId = $operation->getGuid();
        }
        return $this;
    }

    public function getOperationId(): ?string
    {
        return $this->operationId ?? $this->operation?->getGuid();
    }

    public function setOperationId(?string $operationId): self
    {
        $this->operationId = $operationId;
        return $this;
    }

    public function getData(): ?array
    {
        return $this->data;
    }

    public function setData(?array $data): self
    {
        $this->data = $data;
        return $this;
    }

    public function getError(): ?string
    {
        return $this->error;
    }

    public function setError(?string $error): self
    {
        $this->error = $error;
        return $this;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public function setMetadata(array $metadata): self
    {
        $this->metadata = $metadata;
        return $this;
    }

    public function getMetadataValue(string $key, mixed $default = null): mixed
    {
        return $this->metadata[$key] ?? $default;
    }

    public function setMetadataValue(string $key, mixed $value): self
    {
        $this->metadata[$key] = $value;
        return $this;
    }

    public function getTimestamp(): \DateTimeImmutable
    {
        return $this->timestamp;
    }

    /**
     * Создает успешный результат
     */
    public static function success(
        ?AsyncOperationDto $operation = null,
        ?array $data = null,
        array $metadata = []
    ): self {
        return new self(
            status: self::STATUS_SUCCESS,
            operation: $operation,
            data: $data,
            metadata: $metadata
        );
    }

    /**
     * Создает результат с ошибкой
     */
    public static function failed(
        string $error,
        ?AsyncOperationDto $operation = null,
        array $metadata = []
    ): self {
        return new self(
            status: self::STATUS_FAILED,
            operation: $operation,
            error: $error,
            metadata: $metadata
        );
    }

    /**
     * Создает результат в ожидании
     */
    public static function pending(
        ?AsyncOperationDto $operation = null,
        array $metadata = []
    ): self {
        return new self(
            status: self::STATUS_PENDING,
            operation: $operation,
            metadata: $metadata
        );
    }

    /**
     * Создает отмененный результат
     */
    public static function cancelled(
        ?AsyncOperationDto $operation = null,
        array $metadata = []
    ): self {
        return new self(
            status: self::STATUS_CANCELLED,
            operation: $operation,
            metadata: $metadata
        );
    }

    /**
     * Преобразует в массив
     */
    public function toArray(): array
    {
        return [
            'status' => $this->status,
            'operationId' => $this->getOperationId(),
            'data' => $this->data,
            'error' => $this->error,
            'metadata' => $this->metadata,
            'timestamp' => $this->timestamp->format('c'),
        ];
    }
}
